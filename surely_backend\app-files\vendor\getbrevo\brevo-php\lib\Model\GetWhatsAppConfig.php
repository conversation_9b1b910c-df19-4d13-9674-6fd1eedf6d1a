<?php
/**
 * GetWhatsAppConfig
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * GetWhatsAppConfig Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class GetWhatsAppConfig implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'getWhatsAppConfig';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'whatsappBusinessAccountId' => 'string',
        'sendingLimit' => 'string',
        'phoneNumberQuality' => 'string',
        'whatsappBusinessAccountStatus' => 'string',
        'businessStatus' => 'string',
        'phoneNumberNameStatus' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'whatsappBusinessAccountId' => null,
        'sendingLimit' => null,
        'phoneNumberQuality' => null,
        'whatsappBusinessAccountStatus' => null,
        'businessStatus' => null,
        'phoneNumberNameStatus' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'whatsappBusinessAccountId' => 'whatsappBusinessAccountId',
        'sendingLimit' => 'sendingLimit',
        'phoneNumberQuality' => 'phoneNumberQuality',
        'whatsappBusinessAccountStatus' => 'whatsappBusinessAccountStatus',
        'businessStatus' => 'businessStatus',
        'phoneNumberNameStatus' => 'phoneNumberNameStatus'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'whatsappBusinessAccountId' => 'setWhatsappBusinessAccountId',
        'sendingLimit' => 'setSendingLimit',
        'phoneNumberQuality' => 'setPhoneNumberQuality',
        'whatsappBusinessAccountStatus' => 'setWhatsappBusinessAccountStatus',
        'businessStatus' => 'setBusinessStatus',
        'phoneNumberNameStatus' => 'setPhoneNumberNameStatus'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'whatsappBusinessAccountId' => 'getWhatsappBusinessAccountId',
        'sendingLimit' => 'getSendingLimit',
        'phoneNumberQuality' => 'getPhoneNumberQuality',
        'whatsappBusinessAccountStatus' => 'getWhatsappBusinessAccountStatus',
        'businessStatus' => 'getBusinessStatus',
        'phoneNumberNameStatus' => 'getPhoneNumberNameStatus'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    const PHONE_NUMBER_QUALITY_GREEN = 'GREEN';
    const PHONE_NUMBER_QUALITY_YELLOW = 'YELLOW';
    const PHONE_NUMBER_QUALITY_RED = 'RED';
    const PHONE_NUMBER_NAME_STATUS_APPROVED = 'APPROVED';
    const PHONE_NUMBER_NAME_STATUS_PENDING = 'PENDING';
    const PHONE_NUMBER_NAME_STATUS_REJECTED = 'REJECTED';
    

    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getPhoneNumberQualityAllowableValues()
    {
        return [
            self::PHONE_NUMBER_QUALITY_GREEN,
            self::PHONE_NUMBER_QUALITY_YELLOW,
            self::PHONE_NUMBER_QUALITY_RED,
        ];
    }
    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getPhoneNumberNameStatusAllowableValues()
    {
        return [
            self::PHONE_NUMBER_NAME_STATUS_APPROVED,
            self::PHONE_NUMBER_NAME_STATUS_PENDING,
            self::PHONE_NUMBER_NAME_STATUS_REJECTED,
        ];
    }
    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['whatsappBusinessAccountId'] = isset($data['whatsappBusinessAccountId']) ? $data['whatsappBusinessAccountId'] : null;
        $this->container['sendingLimit'] = isset($data['sendingLimit']) ? $data['sendingLimit'] : null;
        $this->container['phoneNumberQuality'] = isset($data['phoneNumberQuality']) ? $data['phoneNumberQuality'] : null;
        $this->container['whatsappBusinessAccountStatus'] = isset($data['whatsappBusinessAccountStatus']) ? $data['whatsappBusinessAccountStatus'] : null;
        $this->container['businessStatus'] = isset($data['businessStatus']) ? $data['businessStatus'] : null;
        $this->container['phoneNumberNameStatus'] = isset($data['phoneNumberNameStatus']) ? $data['phoneNumberNameStatus'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        $allowedValues = $this->getPhoneNumberQualityAllowableValues();
        if (!is_null($this->container['phoneNumberQuality']) && !in_array($this->container['phoneNumberQuality'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'phoneNumberQuality', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        $allowedValues = $this->getPhoneNumberNameStatusAllowableValues();
        if (!is_null($this->container['phoneNumberNameStatus']) && !in_array($this->container['phoneNumberNameStatus'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'phoneNumberNameStatus', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets whatsappBusinessAccountId
     *
     * @return string
     */
    public function getWhatsappBusinessAccountId()
    {
        return $this->container['whatsappBusinessAccountId'];
    }

    /**
     * Sets whatsappBusinessAccountId
     *
     * @param string $whatsappBusinessAccountId Id of the WhatsApp business account
     *
     * @return $this
     */
    public function setWhatsappBusinessAccountId($whatsappBusinessAccountId)
    {
        $this->container['whatsappBusinessAccountId'] = $whatsappBusinessAccountId;

        return $this;
    }

    /**
     * Gets sendingLimit
     *
     * @return string
     */
    public function getSendingLimit()
    {
        return $this->container['sendingLimit'];
    }

    /**
     * Sets sendingLimit
     *
     * @param string $sendingLimit Sending limit Information of the WhatsApp API account
     *
     * @return $this
     */
    public function setSendingLimit($sendingLimit)
    {
        $this->container['sendingLimit'] = $sendingLimit;

        return $this;
    }

    /**
     * Gets phoneNumberQuality
     *
     * @return string
     */
    public function getPhoneNumberQuality()
    {
        return $this->container['phoneNumberQuality'];
    }

    /**
     * Sets phoneNumberQuality
     *
     * @param string $phoneNumberQuality Quality status of phone number associated with WhatsApp account. There are three quality ratings. example - **High (GREEN) , Medium (YELLOW) and Low(RED)**
     *
     * @return $this
     */
    public function setPhoneNumberQuality($phoneNumberQuality)
    {
        $allowedValues = $this->getPhoneNumberQualityAllowableValues();
        if (!is_null($phoneNumberQuality) && !in_array($phoneNumberQuality, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'phoneNumberQuality', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['phoneNumberQuality'] = $phoneNumberQuality;

        return $this;
    }

    /**
     * Gets whatsappBusinessAccountStatus
     *
     * @return string
     */
    public function getWhatsappBusinessAccountStatus()
    {
        return $this->container['whatsappBusinessAccountStatus'];
    }

    /**
     * Sets whatsappBusinessAccountStatus
     *
     * @param string $whatsappBusinessAccountStatus Status information related to WhatsApp Api account
     *
     * @return $this
     */
    public function setWhatsappBusinessAccountStatus($whatsappBusinessAccountStatus)
    {
        $this->container['whatsappBusinessAccountStatus'] = $whatsappBusinessAccountStatus;

        return $this;
    }

    /**
     * Gets businessStatus
     *
     * @return string
     */
    public function getBusinessStatus()
    {
        return $this->container['businessStatus'];
    }

    /**
     * Sets businessStatus
     *
     * @param string $businessStatus Verification status information of the Business account
     *
     * @return $this
     */
    public function setBusinessStatus($businessStatus)
    {
        $this->container['businessStatus'] = $businessStatus;

        return $this;
    }

    /**
     * Gets phoneNumberNameStatus
     *
     * @return string
     */
    public function getPhoneNumberNameStatus()
    {
        return $this->container['phoneNumberNameStatus'];
    }

    /**
     * Sets phoneNumberNameStatus
     *
     * @param string $phoneNumberNameStatus Status of the name associated with WhatsApp Phone number
     *
     * @return $this
     */
    public function setPhoneNumberNameStatus($phoneNumberNameStatus)
    {
        $allowedValues = $this->getPhoneNumberNameStatusAllowableValues();
        if (!is_null($phoneNumberNameStatus) && !in_array($phoneNumberNameStatus, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'phoneNumberNameStatus', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['phoneNumberNameStatus'] = $phoneNumberNameStatus;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


