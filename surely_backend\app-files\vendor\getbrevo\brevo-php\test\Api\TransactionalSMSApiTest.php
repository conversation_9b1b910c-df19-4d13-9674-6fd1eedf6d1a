<?php
/**
 * TransactionalSMSApiTest
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 3.0.68
 */
/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the endpoint.
 */

namespace Brevo\Client;

use Brevo\Client\Configuration;
use Brevo\Client\ApiException;
use Brevo\Client\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * TransactionalSMSApiTest Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class TransactionalSMSApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for getSmsEvents
     *
     * Get all your SMS activity (unaggregated events).
     *
     */
    public function testGetSmsEvents()
    {
    }

    /**
     * Test case for getTransacAggregatedSmsReport
     *
     * Get your SMS activity aggregated over a period of time.
     *
     */
    public function testGetTransacAggregatedSmsReport()
    {
    }

    /**
     * Test case for getTransacSmsReport
     *
     * Get your SMS activity aggregated per day.
     *
     */
    public function testGetTransacSmsReport()
    {
    }

    /**
     * Test case for sendAsyncTransactionalSms
     *
     * Send SMS message asynchronously to a mobile number.
     *
     */
    public function testSendAsyncTransactionalSms()
    {
    }

    /**
     * Test case for sendTransacSms
     *
     * Send SMS message to a mobile number.
     *
     */
    public function testSendTransacSms()
    {
    }
}
