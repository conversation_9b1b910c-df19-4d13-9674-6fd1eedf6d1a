import { useEffect } from 'react';
import { useAuthContext } from 'src/context/AuthContext';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { requestVerificationEmail } from 'src/services/user';

export const EmailVerificationBanner = () => {
  const { user } = useAuthContext();
  const toastSystem = useToastSystem();
  
  useEffect(() => {
  }, [user]);

  if (user?.profile?.email_verified) {
    return null;
  }
  
  if (!user?.profile?.email) {
    return null;
  }

  const handleVerifyNow = async () => {
    try {
      const result = await requestVerificationEmail({ 
        email: user.profile.email,
        user_id: user.profile.id
      });
      
      if (result.success) {
        toastSystem.showSuccess('Success', 'Verification email has been sent. Please check your inbox.');
        window.location.href = '/verify-email';
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toastSystem.showError('Error', (error as Error).message || 'Failed to send verification email');
    }
  };

  return (
    <div className="bg-yellow-100 border-l-4 border-yellow-500 p-4">
      <div className="flex justify-between items-center">
        <p className="text-yellow-700 flex items-center gap-2">
          Please verify your email within 7 days from your account creation to maintain full access.
          <div className="relative group">
            <svg 
              className="w-5 h-5 text-yellow-700 cursor-help" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
            <div className="absolute top-full hidden group-hover:block w-64 p-3 bg-white text-gray-800 border border-gray-200 text-xs rounded-lg shadow-lg z-50">
              After 7 days you will be logged out. To ensure uninterrupted access and avoid disruptions, 
              please verify your account before making any action.
            </div>
          </div>
        </p>
        <div className="flex gap-2">
          <button 
            className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
            onClick={handleVerifyNow}
          >
            Verify Now
          </button>
          <button 
            className="text-yellow-700 hover:text-yellow-800 bg-white border border-yellow-500 rounded-md px-4 py-2"
            onClick={() => document.querySelector('.bg-yellow-100')?.remove()}
          >
            Later
          </button>
        </div>
      </div>
    </div>
  );
};