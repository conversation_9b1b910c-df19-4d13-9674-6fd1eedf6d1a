// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { Text, View, Button, Divider, Modal, Select, Image } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { getLanguages, getLanguagesLevel } from 'src/services/settings';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../../assets/icons/surleyicon/surleyicon.png';

interface Language {
  id: number;
  name: string;
}

interface AddLanguageProfileDetailsProps {
  active: boolean;
  deactivate: () => void;
  addLanguage: (data: any) => void;
}

const AddLanguageProfileDetails: React.FC<AddLanguageProfileDetailsProps> = ({ active, deactivate, addLanguage }) => {
  const toastSystem = useToastSystem();
  const [languages, setLanguages] = useState<Language[]>([]);
  const [levels, setLevels] = useState<Language[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [selectedLevel, setSelectedLevel] = useState<string>('');
  const [error, setError] = useState('');

  useEffect(() => {
    getLanguages().then((data) => {
      setLanguages(data.data);
    });
    getLanguagesLevel().then((data) => {
      setLevels(data.data);
    });
  }, []);

  const handleLanguageChange = (value: string) => {
    setSelectedLanguage(value);
  };

  const handleLevelChange = (value: string) => {
    setSelectedLevel(value);
  };

  const handleAddClick = () => {
    if (!selectedLanguage && !selectedLevel) {
      setError('Please select a language and proficiency level. ');
      toastSystem.showError('Missing Selection', 'Please select a language and proficiency level.');
    } else if (!selectedLanguage) {
      setError('Please select a language. ');
      toastSystem.showError('Missing Language', 'Please select a language.');
    } else if (!selectedLevel) {
      setError('Please select a proficiency level. ');
      toastSystem.showError('Missing Level', 'Please select a proficiency level.');
    } else {
      addLanguage({
        language: languages.find((language) => language.id === parseInt(selectedLanguage)),
        level: levels.find((level) => level.id === parseInt(selectedLevel)),
      });
      deactivate();
    }
  };

  useEffect(() => {
    setError('');
  }, [selectedLanguage, selectedLevel]);
  

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Add language</Text>
        </View>
        <View className='mt-[16px] flex flex-col '>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Language</Text>
          <Select
            name='language'
            className='mt-[4px] h-[48px] w-full sm:w-[376px]'
            options={
              Array.isArray(languages)
                ? languages.map((language) => ({
                    label: language.name,
                    value: language.id.toString(),
                  }))
                : []
            }
            placeholder='Select language name'
            onChange={(args) => handleLanguageChange(args.value)}
          />
        </View>
        <View className='mt-[16px] flex flex-col'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>Proficiency</Text>
          <Select
            name='proficiency'
            className='mt-[4px] h-[48px] w-full sm:w-[376px]'
            options={
              Array.isArray(levels)
                ? levels.map((level) => ({
                    label: level.name,
                    value: level.id.toString(),
                  }))
                : []
            }
            placeholder='Select level of proficiency'
            onChange={(args) => handleLevelChange(args.value)}
          />
        </View>
        <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>{error}</Text>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-end'>
          <Button
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutralrounded-[8px] mr-[10px] flex h-[48px] w-[180px] items-center justify-center gap-2 border border-[#DFE2EA] !bg-transparent px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            className='flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] !bg-[#0B80E7] px-4 py-2'
            onClick={handleAddClick}
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Add</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default AddLanguageProfileDetails;
