// @ts-nocheck
import React, { useState, useContext } from 'react';
import { Modal, Image, Text, View, Button, Divider, Icon } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { Link, useNavigate } from 'react-router-dom';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { format, isValid, parseISO } from 'date-fns';
import { getValidImageUrl } from '../../utils/';
import NoJobPhoto from 'src/components/NoData/NoJobPhoto';
import surelyproicon1 from '../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../assets/icons/surelyproicon/surelyproicon7.svg';
import inclusivitypledgeicon from '../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import { JobContext } from 'src/context/JobContext';
import BadgeEmergencyHire from 'src/components/cards/JobCard/BadgeEmergencyHire/BadgeEmergencyHire';

interface JobsModalMapSearchProps {
  active: boolean;
  deactivate: () => void;
  selectedJob: any | null;
}

const JobsModalMapSearch: React.FC<JobsModalMapSearchProps> = ({ active, deactivate, selectedJob }) => {
  const { addFavorite, removeFavorite, fetchAllJobs } = useContext(JobContext);
  const navigate = useNavigate();
  const toastSystem = useToastSystem();
  const [isFavourite, setIsFavourite] = useState(selectedJob?.is_favorite || false);

  const [type, setType] = useState();
  const [showMoreQualifications, setShowMoreQualifications] = useState(false);
  const siaLicenceArray = selectedJob?.sia_licence || [];
  const industrySectorArray = selectedJob?.industry_sector || [];
  const surelyProBadgeArray = selectedJob?.surely_pro_badge || [];

  const array = [
    ...siaLicenceArray.map((sia: any) => ({ value: sia, type: 'siaLicense' })),
    ...industrySectorArray.map((industry: any) => ({ value: industry, type: 'industrySector' })),
    ...surelyProBadgeArray.map((badge: any) => ({ value: badge, type: 'surelyProBadge' })),
  ];

  const geticonSrc = (type: any) => {
    switch (type) {
      case 'Customer Service':
        return surelyproicon3;
      case 'Use Of Equipment':
        return surelyproicon2;
      case 'Disability Focus':
        return surelyproicon6;
      case 'Substance Awareness':
        return surelyproicon4;
      case 'Vulnerable People':
        return surelyproicon5;
      case 'Conflict Managament':
        return surelyproicon7;
      default:
        return surelyproicon1;
    }
  };

  // const calculateTotalHours = (dateRanges: any[]): number => {
  //   let allHours = 0;
  //   dateRanges?.forEach((dateRange) => {
  //     const start = new Date(dateRange.start);
  //     const end = new Date(dateRange.end);
  //     const durationInMilliseconds = end.getTime() - start.getTime();
  //     const durationInHours = durationInMilliseconds / (1000 * 60 * 60);
  //     allHours += durationInHours;
  //   });

  //   return allHours;
  // };
  const calculateTotalHours = (dateRanges: any[]): number => {
    let allHours = 0;
    dateRanges?.forEach((dateRange) => {
      const start = new Date(dateRange.start);
      const end = new Date(dateRange.end);
      const durationInMilliseconds = end.getTime() - start.getTime();
      const durationInHours = durationInMilliseconds / (1000 * 60 * 60);
      allHours += durationInHours;
    });

    return allHours;
  };

  const totalHours = calculateTotalHours(selectedJob?.date_range);
  const getEarningsMin = (totalHours * selectedJob?.hourly_rate_min).toFixed(2);

  const getImageSrc = (images: any) => {
    for (const image of images) {
      if (image) {
        return image.startsWith('https://app.surelysecurity.com/storage/') ? image : 'https://app.surelysecurity.com/storage/' + image;
      }
    }
    return null;
  };

  const selectedImageSrc = selectedJob?.images ? getImageSrc(selectedJob.images) : null;

  return (
    <Modal active={active} onClose={deactivate} className='!w-[312px] '>
      {selectedJob && (
        <>
          <View className='flex h-[100%] flex-col items-center gap-5'>
            <View className='flex w-full items-center justify-between'>
              <View className='flex items-center'>{selectedJob?.is_emergency_hire && <BadgeEmergencyHire />}</View>
              <span
                onClick={() => {
                  setIsFavourite((prevFavourite: any) => !prevFavourite);
                  if (isFavourite) {
                    removeFavorite(selectedJob?.id);
                    toastSystem.showSuccess('Done!', 'Removed from favorites');
                    fetchAllJobs();
                  } else {
                    addFavorite(selectedJob?.id);
                    toastSystem.showSuccess('Done!', 'Added to favorites');
                    fetchAllJobs();
                  }
                }}
                className={`material-icons-outlined  h-[18.488px] w-[20.15px]  ${isFavourite ? 'text-red-500' : 'text-[#BBC1D3]'}`}
              >
                favorite
              </span>
            </View>

            <Link to={`/operator-job/${selectedJob?.id}`}>
              <View>
                <View className='relative h-[176px] w-full'>
                  {selectedImageSrc ? (
                    <img className='absolute inset-0 h-full w-full rounded-xl rounded-xl object-cover' src={selectedImageSrc} alt='Job Image' />
                  ) : (
                    <NoJobPhoto />
                  )}
                </View>
              </View>
              <View className='flex w-full flex-col justify-start gap-1'>
                <View className='mt-[14px] h-[40px]'>
                  <Text className='rubik text-[16px] font-medium leading-[20px] text-[#1A1A1A]  '>{selectedJob?.post_name}</Text>
                </View>
                <View>
                  <Text className='rubik my-3 text-[24px] font-medium leading-[32px] text-[#383838]'>£{selectedJob?.hourly_rate_min}/h</Text>
                </View>
                <View className='flex h-[60px] gap-1 '>
                  <Text className='rubik flex flex-none text-[14px] font-medium leading-5 text-[#383838] '>I need:</Text>
                  <Text className='rubik text-[14px] font-medium leading-5 text-[#0B80E7]'>
                    {/* {job.sia_licence && job.sia_licence.join(', ')} */}
                    {selectedJob?.title}
                  </Text>
                </View>
                <View direction={'row'} align={'center'} className='my-1'>
                  <span className='material-icons-outlined  w-1/12 text-sm text-[#383838]'>calendar_today</span>

                  <View className='w-11/12'>
                    <View className='w-11/12'>
                      <Text className='rubik text-[14px] font-normal text-[#383838]'>
                        From&nbsp;
                        <span className='rubik text-[14px] font-medium text-[#383838]'>
                          {selectedJob?.date_range.length > 0 &&
                            isValid(parseISO(selectedJob?.date_range[0].start)) &&
                            format(parseISO(selectedJob?.date_range[0].start), 'dd MMM yyyy')}
                        </span>
                        &nbsp;to&nbsp;
                        <span className='rubik text-[14px] font-medium text-[#383838]'>
                          {selectedJob?.date_range.length > 0 &&
                            isValid(parseISO(selectedJob?.date_range[selectedJob?.date_range.length - 1].end)) &&
                            format(parseISO(selectedJob?.date_range[selectedJob?.date_range.length - 1].end), 'dd MMM yyyy')}
                        </span>
                      </Text>
                    </View>
                  </View>
                  <View className={'my-2'}>
                    <Text className='rubik text-sm font-normal leading-5 text-[#383838]'>Published&nbsp;{selectedJob?.published}</Text>
                  </View>
                </View>
                <View gap={2} className='rounded-[8px] bg-[#F4F5F7]'>
                  <View direction={'row'} gap={2} className='ml-[10px] mt-[10px] items-center'>
                    {selectedJob?.city ? (
                      <View direction={'row'} gap={2} className='items-center'>
                        <span className='material-icons-outlined text-base text-[#3C455D]'>place</span>
                        <Text className='rubik break-all text-sm font-normal leading-5 text-[#3C455D]'>{selectedJob?.city}</Text>
                      </View>
                    ) : (
                      <></>
                    )}
                    <View direction={'row'} align={'center'} gap={2} className='items-center'>
                      <span className='material-icons text-base text-[#3C455D]'>person</span>
                      <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>{selectedJob?.nr_of_operatives} People</Text>
                    </View>
                  </View>
                  <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                    <span className='material-icons-outlined text-base text-[#3C455D]'>credit_card</span>
                    <View direction={'row'} gap={1}>
                      <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>Earnings:</Text>
                      <Text className='rubik text-sm font-medium leading-5 text-[#3C455D]'>£{getEarningsMin}</Text>
                    </View>
                  </View>
                  <View direction={'row'} align={'center'} gap={2} className='ml-[10px]'>
                    <span className='material-icons-outlined text-base text-[#3C455D]'>lock</span>
                    <View direction={'row'} gap={1}>
                      <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>Escrow deposit:</Text>
                      <Text className='rubik text-sm font-medium leading-5 text-[#3C455D]'>{selectedJob?.escrow_deposit}%</Text>
                    </View>
                  </View>
                  <View direction={'row'} align={'center'} gap={2} className='mb-[10px] ml-[10px]'>
                    <span className='material-icons-outlined text-base text-[#3C455D]'>payments</span>
                    <View direction={'row'} gap={1}>
                      <Text className='rubik text-sm font-normal leading-5 text-[#3C455D]'>Payment terms:</Text>
                      <Text className='rubik text-sm font-medium leading-5 text-[#3C455D]'>{selectedJob?.payment_terms ? `${selectedJob?.payment_terms} days` : '10 days'}</Text>
                    </View>
                  </View>
                </View>
              </View>
            </Link>
            <Divider className='mt-[16px] h-[1px] w-full'></Divider>

            <View className={`flex w-full flex-wrap   gap-2 ${showMoreQualifications ? 'h-auto' : 'h-[80px]'}`}>
              {(array ?? []).slice(0, !showMoreQualifications ? 4 : (array?.length || 0)).map((qualification) => {
                switch (qualification.type) {
                  case 'siaLicense':
                    return (
                      <Button
                        key={qualification.value}
                        size='small'
                        rounded={true}
                        elevated={false}
                        className='h-[18px] max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58] '
                      >
                        <Text color='positive' className='rubik flex items-center gap-1 '>
                          <span className='material-icons text-[16px]'>star</span>
                          {qualification.value}
                        </Text>
                      </Button>
                    );
                  case 'industrySector':
                    return (
                      <Button
                        key={qualification.value}
                        size='small'
                        rounded={true}
                        elevated={false}
                        className='h-[18px] max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs '
                      >
                        <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{qualification.value}</Text>
                      </Button>
                    );
                  case 'surelyProBadge':
                    return (
                      <Button
                        key={qualification.value}
                        size='small'
                        rounded={true}
                        elevated={false}
                        className='h-[18px] max-w-xs overflow-hidden truncate  border !bg-[#DDEFFF] px-2 py-1 text-xs'
                      >
                        <View className='flex flex-row'>
                          {qualification.value && <img src={geticonSrc(qualification.value)} alt={qualification.value} className='mr-2' />}
                          <Text className='rubik font-normal leading-4 text-[#053D6D]'>{qualification.value}</Text>
                        </View>
                      </Button>
                    );
                  default:
                    break;
                }
              })}
              {selectedJob?.is_inclusivity_pledge && (
                <Button
                  size='small'
                  rounded={true}
                  elevated={false}
                  variant='outline'
                  className='border-dark-gradient h-[18px] truncate border !bg-[#ffff] px-2 py-1 text-xs '
                  icon={() => <img src={inclusivitypledgeicon} className='w-[16px]' />}
                >
                  <Text className='rubik font-normal leading-4 !text-[#323C58] text-[#FFFFFF]'>Inclusivity Pledge</Text>
                </Button>
              )}
            </View>
            {array.length > 4 ? (
              <button
                className='btn-no-hover rubik flex h-[40px] flex-1 items-end text-[16px] font-[500] leading-[24px] text-[#0B80E7]'
                onClick={() => setShowMoreQualifications((prevState) => !prevState)}
              >
                {showMoreQualifications ? 'Show less' : 'Show more'}
              </button>
            ) : (
              <div className='h-[43px]' />
            )}
          </View>

          <Divider className='mt-[20px] h-[1px] w-full' />
          <View className='mt-[20px] flex  justify-end'>
            <button
              onClick={() => navigate(`/operator-job/${selectedJob.id}`)}
              className='btn-no-hover border-neutral bg-background-base flex flex-row gap-2 rounded border'
            >
              <Text className='rubik text-medium text-[16px] leading-[24px] !text-[#0B80E7] '>More </Text>
              <div className='mt-[5px]'>
                <svg xmlns='http://www.w3.org/2000/svg' width='9' height='14' viewBox='0 0 9 14' fill='none'>
                  <path d='M0.25 12.5554L1.43607 13.7415L8.07 7.10757L1.43607 0.473633L0.25 1.6597L5.69787 7.10757L0.25 12.5554Z' fill='#0B80E7' />
                </svg>
              </div>
            </button>
          </View>
        </>
      )}
    </Modal>
  );
};
export default JobsModalMapSearch;
