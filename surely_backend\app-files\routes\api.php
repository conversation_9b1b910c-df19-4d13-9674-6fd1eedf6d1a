<?php

use App\Http\Controllers\Api\AccountSettingsController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AvailabilityController;
use App\Http\Controllers\Api\BroadcastController;
use App\Http\Controllers\Api\Buyer\BookingController;
use App\Http\Controllers\Api\Buyer\FormController;
use App\Http\Controllers\Api\Buyer\ProviderController;
use App\Http\Controllers\Api\CardController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\ContractController;
use App\Http\Controllers\Api\EmploymentController;
use App\Http\Controllers\Api\ExperienceController;
use App\Http\Controllers\Api\FavouriteController;
use App\Http\Controllers\Api\Freelancer\JobController;
use App\Http\Controllers\Api\EmailController;
use App\Http\Controllers\Api\GeneralController;
use App\Http\Controllers\Api\MobileUserRatingController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\PaymentDetailsController;
use App\Http\Controllers\Api\ProfileDetailsController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\SiaCertificateController;
use App\Http\Controllers\Api\SupportController;
use App\Http\Controllers\Api\SurelyProBadgeController;
use App\Http\Controllers\Api\BlogController;
use App\Http\Controllers\Api\NewsLetterController;
use App\Http\Controllers\MobileUserController;
use App\Http\Resources\FavoriteCollection;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Models\Message;
use App\Http\Controllers\Auth\GuestLoginController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('register', [AuthController::class, 'register']);
Route::post('login', [AuthController::class, 'login']);
Route::post('send_forgot_password', [AuthController::class, 'send_forgot_password']);
Route::post('confirm_forgot_code', [AuthController::class, 'confirm_forgot_code']);
Route::post('new_password', [AuthController::class, 'new_password']);
Route::post('email', [EmailController::class, 'sendEmail']);
Route::get('postcode/{postcode}', [GeneralController::class, 'postalCode']);
// Route::get('sia-licence-validation/{sia_licence}', [GeneralController::class, 'siaLicenceValidation']);
Route::resource('api_blogs', BlogController::class);
Route::post('send_code', [AuthController::class, 'send_code']);
Route::post('verify_code', [AuthController::class, 'verify_code']);
Route::post('verify_email', [AuthController::class, 'verify_email']);
Route::post('send_verification_email', [AuthController::class, 'send_verification_email']);
Route::post('newsletter', [NewsLetterController::class, 'subscribe']);
Route::post('check_email', [AuthController::class, 'check_email']);

Route::middleware('auth:api')->group(function () {
    Route::get('invoices/{id}', [ContractController::class, 'contractInvoice']);
    Route::get('invoices', [ContractController::class, 'contractInvoicesList']);

    //save bank account
    Route::post('save_bank_account', [PaymentController::class, 'save_bank_account']);
    Route::post('save_utr_vat', [PaymentController::class, 'save_utr_vat']);
    Route::get('get_bank_account', [PaymentController::class, 'get_bank_account']);
    Route::post('contracts/{id}/pay', [PaymentController::class, 'payContract']);
    Route::post('/contracts/{contract}/pay-escrow', [PaymentController::class, 'pay']);

    Route::get('/ratings/users/{userId?}/profile', [MobileUserRatingController::class, 'index']);
    Route::post('/ratings/users/{userId}/profile', [MobileUserRatingController::class, 'store']);
    Route::post('change_password', [AuthController::class, 'change_password']);
    Route::get('expertise_fields', [AuthController::class, 'expertise_fields']);
    Route::get('gigs', [AuthController::class, 'gigs']);
    Route::post('freelancer_profile', [AuthController::class, 'freelancer_profile']);
    Route::post('business_profile', [AuthController::class, 'business_profile']);
    Route::get('update_location/{lat}/{lng}/{cityId}', [AuthController::class, 'update_location']);
    Route::get('cities', [AuthController::class, 'cities']);
    Route::post('sms', [EmailController::class, 'sendSMS']);
    Route::get('operatives', [MobileUserController::class, 'operatives']); // TODO: Duhet zevendesuar me api/users?type=operatives
    Route::get('profile', [MobileUserController::class,'profile']);
    Route::post('register_verification', [AuthController::class, 'registerVerification']);
    Route::post('register_verification_client', [AuthController::class, 'registerVerificationClient']);

    Route::prefix('settings')->group(function () {
        Route::get('/', [AccountSettingsController::class, 'getProfileSettings']);
        Route::post('/', [AccountSettingsController::class, 'updateProfileSettings']);
        Route::get('languages', [AccountSettingsController::class, 'getAllLanguages']);
        Route::get('user_languages', [AccountSettingsController::class, 'getLanguages']);
        Route::post('user_languages', [AccountSettingsController::class, 'storeLanguage']);
        Route::delete('user_languages/{id}', [AccountSettingsController::class, 'deleteLanguage']);
        Route::resource('employment', EmploymentController::class);
        Route::get('availability', [AvailabilityController::class, 'index']);
        Route::patch('availability', [AvailabilityController::class, 'availability']);
        Route::resource('payment-details', PaymentDetailsController::class);
        Route::resource('profile-details', ProfileDetailsController::class);
        Route::resource('sia-certificate', SiaCertificateController::class);
        Route::get('general', [GeneralController::class, 'index']);
        Route::post('general', [GeneralController::class, 'update']);
        Route::delete('delete-account', [AuthController::class,'deleteAccount']);
        Route::resource('surely-pro-badges', SurelyProBadgeController::class);
        Route::get('notifications', [NotificationController::class, 'index']);
        Route::post('notifications', [NotificationController::class, 'store']);
        Route::post('request_email_change', [AccountSettingsController::class, 'requestEmailChange']);
    });
 
    Route::post('add_card', [CardController::class, 'add_card']);
    Route::get('my_cards', [CardController::class, 'my_cards']);
    Route::get('delete_card/{id}', [CardController::class, 'destroy']);

    Route::get('my_experiences', [ExperienceController::class, 'experiences']);
    Route::post('add_experience', [ExperienceController::class, 'add_experience']);
    Route::post('edit_experience/{id}', [ExperienceController::class, 'edit_experience']);
    Route::get('remove_experience/{id}', [ExperienceController::class, 'remove_experience']);

    Route::get('service_providers', [ProviderController::class, 'service_providers']);

    Route::post('location_filter', [ProviderController::class, 'location_filter']);
    Route::get('freelancer_info/{id}', [ProviderController::class, 'freelancer_info']);

    Route::post('fill_form', [FormController::class, 'store_form']);
    Route::get('my_positions', [FormController::class, 'my_positions']);
    Route::get('close_position/{id}', [FormController::class, 'close_position']);
    Route::post('book_applicant', [FormController::class, 'book_applicant']);

    Route::get('user_blocked_dates/{id}', [BookingController::class, 'user_blocked_dates']);
    Route::get('booking_notes', [BookingController::class, 'booking_notes']);
    Route::post('store_booking', [BookingController::class, 'store_booking']);
    Route::get('my_bookings', [BookingController::class, 'my_bookings']);

    Route::get('user_events', [\App\Http\Controllers\Api\Freelancer\BookingController::class, 'user_events']);
    Route::get('change_status/{id}/{status}', [\App\Http\Controllers\Api\Freelancer\BookingController::class, 'change_status']);
    Route::get('calendar_events', [\App\Http\Controllers\Api\Freelancer\BookingController::class, 'calendar_events']);
    Route::post('add_event', [\App\Http\Controllers\Api\Freelancer\BookingController::class, 'add_event']);

    Route::get('open_positions', [JobController::class, 'open_positions']); //TODO: Kjo duhet te zevendesohet me nje filter te jobs : /api/jobs?status=open
    Route::post('apply_to_position', [JobController::class, 'apply_to_position']); //TODO:  kjo eshte api per mobile por po shtojme edhe nje api extra per standart
    Route::get('jobs/{jobId}/applications/', [JobController::class, 'applicants']);
    Route::post('jobs/{jobId}/applications/', [JobController::class, 'apply_to_position']);
    Route::get('position_applicants/{id}', [JobController::class, 'applicants']); //API mobile
    Route::post('/jobs/{job}/applicants/{applicant}/status', [JobController::class, 'changeStatus']);
    Route::post('chats/0/invite/{operatorId}/for-jobs/', [JobController::class, 'sendInvitation']);
    Route::get('/contracts/payments', [ContractController::class, 'payments']);
    Route::get('/my_invoices', [ContractController::class, 'myInvoices']);
    Route::get('/contracts/{contractId}/invoices', [ContractController::class, 'contractInvoices']);

    Route::post('/jobs/{jobId}/favorite', [JobController::class, 'addToFavorites']);
    Route::delete('/jobs/{jobId}/favorite', [JobController::class, 'removeFromFavorites']);
    Route::resource('jobs', JobController::class);
    Route::post('chats/{chat}/contracts', [ContractController::class, 'storeFromChat']);
    Route::post('chats/{chatId}/contract/{contractId}/status', [ContractController::class, 'changeStatus']);
    Route::resource('contracts', ContractController::class);
    Route::post('contracts/{contractId}/payments', [PaymentController::class, 'index']);
    Route::post('contracts/{contractId}/payments', [PaymentController::class, 'store']);
    Route::put('contracts/{contractId}/shifts', [ContractController::class, 'updateShifts']);
    Route::post('contracts/{id}/reviews', [ReviewController::class, 'store']);
    Route::get('user/{id}/reviews', [ReviewController::class, 'index']);


    Route::post('contracts/{id}/supports', [SupportController::class, 'store']);

    Route::get('get_chat/{userId?}', [ChatController::class, 'getMessagesByUser']); //TODO: Po i leme keto API ekzistent, por llogjiken po e kalojme tek metodat e reja, userId duhet te jete opsionale, duhet te jete vetem auth()->id()
    Route::get('my_chats', [ChatController::class, 'index']);
    Route::post('send_message', [ChatController::class, 'sendMessage']); //TODO: E mira eshte qe te kemi chatId gjithmone

    // Route::get('/chats', [ChatController::class, 'index'])->name('chat.index');
    Route::get('/chats', [ChatController::class, 'getChatList']);
    // Route::get('/chats/{chatId}', [ChatController::class, 'getChatDetails']);
    Route::get('/chats/{chatId}', [ChatController::class, 'show'])->name('chat.show');
    Route::get('/chats/{chatId}/messages', [ChatController::class, 'getChatMessages'])->name('chat.messages');
    Route::post('/chats/{chatId}/messages', [ChatController::class, 'sendMessage'])->name('chat.messages.store');

    // Route::get('/chats', [ChatController::class, 'index'])->name('chat.index');
    // Route::get('/chats/{id}', [ChatController::class, 'show'])->name('chat.show');
    // Route::get('/chats/{id}/messages', [ChatController::class, 'getMessages'])->name('chat.messages');
    // Route::post('/chats/{id}/messages', [ChatController::class, 'sendMessage'])->name('chat.messages.store');


    Route::delete('/chat/delete/{id}', [ChatController::class, 'destroy'])->name('chat.destroy');
    Route::post('/broadcasting/auth', [BroadcastController::class, 'authenticate']);
    Route::resource('favourites', FavouriteController::class);

    Route::get('sia-licence-validation/{sia_licence}', [GeneralController::class, 'siaLicenceValidation']);
    Route::post('reports', [ReportController::class, 'store']);

    Route::get('/referal-code', [AuthController::class, 'getReferalLink']);

    Route::post('stripe/webhook', [App\Http\Controllers\Api\PaymentController::class, 'handleStripeWebhook']);

    Route::get('/chat/{chatId}/messages', function (Request $request, $chatId) {
        $page = $request->query('page', 1);
        $pageSize = 50;

        return Message::where('chat_id', $chatId)
            ->with(['sender:id,name,profile_photo', 'receiver:id,name,profile_photo'])
            ->orderBy('created_at', 'desc')
            ->paginate($pageSize);
    });

    Route::post('/upgrade-to-client', [AuthController::class, 'upgradeToClient']);

});

Route::fallback(function () {
    return response()->json([
        'error' => true,
        'message' => 'Route not found!',
    ], 404);
});

Route::get('users/operatives', [MobileUserController::class, 'topProfiles']);
Route::get('users/{userId}/operative', [MobileUserController::class, 'userDetails']);

Route::post('/users/{userId}/favorite', [MobileUserController::class, 'addToFavorites']);
Route::delete('/users/{userId}/favorite', [MobileUserController::class, 'removeFromFavorites']);

Route::get('top-profiles', [MobileUserController::class, 'topProfiles']);
Route::get('top-rated-profiles', [MobileUserController::class, 'topRatedProfiles']);

Route::post('/guest-login', [GuestLoginController::class, 'login']);



// Route::delete('stripe-accounts/{accountId}', [PaymentController::class, 'deleteStripeAccount']);
Route::delete('stripe-accounts/name/{name}', [PaymentController::class, 'deleteStripeAccountsByName']);

Route::post('stripe-accounts/bulk-delete', [PaymentController::class, 'deleteMultipleStripeAccounts']);

Route::get('stripe-accounts', [PaymentController::class, 'getAllStripeAccounts']);

Route::get('stripe-accounts/restricted', [PaymentController::class, 'getRestrictedAccounts']);