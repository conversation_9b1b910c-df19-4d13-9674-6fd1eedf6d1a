<?php

require_once 'vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

use Brevo\Client\Configuration;
use Brevo\Client\Api\TransactionalEmailsApi;
use Brevo\Client\Model\SendSmtpEmail;
use GuzzleHttp\Client;

echo "Testing Brevo Email Service Integration...\n";

try {
    // Test 1: Check if API key is configured
    $apiKey = $_ENV['BREVO_API_KEY'] ?? null;
    if (!$apiKey) {
        echo "❌ BREVO_API_KEY not found in environment variables\n";
        exit(1);
    }
    echo "✅ API Key found\n";

    // Test 2: Initialize Brevo client
    $config = Configuration::getDefaultConfiguration()->setApiKey('api-key', $apiKey);
    $apiInstance = new TransactionalEmailsApi(new Client(), $config);
    echo "✅ Brevo client initialized\n";

    // Test 3: Create a test email (don't send)
    $sendSmtpEmail = new SendSmtpEmail();
    $sendSmtpEmail->setTo([
        ['email' => '<EMAIL>', 'name' => 'Test User']
    ]);
    $sendSmtpEmail->setSender([
        'email' => $_ENV['NO_REPLY_EMAIL'] ?? '<EMAIL>',
        'name' => 'Surely Test'
    ]);
    $sendSmtpEmail->setSubject('Test Email');
    $sendSmtpEmail->setHtmlContent('<p>This is a test email from Brevo integration.</p>');
    echo "✅ Email object created successfully\n";

    echo "\n🎉 Brevo integration test completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Update your .env file with the provided Brevo credentials\n";
    echo "2. Test sending actual emails through the API endpoints\n";
    echo "3. Monitor email delivery in your Brevo dashboard\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
