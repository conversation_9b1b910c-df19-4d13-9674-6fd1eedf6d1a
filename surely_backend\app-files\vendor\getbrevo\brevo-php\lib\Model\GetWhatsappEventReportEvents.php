<?php
/**
 * GetWhatsappEventReportEvents
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * GetWhatsappEventReportEvents Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class GetWhatsappEventReportEvents implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'getWhatsappEventReport_events';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'contactNumber' => 'string',
        'date' => 'string',
        'messageId' => 'string',
        'event' => 'string',
        'reason' => 'string',
        'body' => 'string',
        'mediaUrl' => 'string',
        'senderNumber' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'contactNumber' => 'mobile',
        'date' => null,
        'messageId' => null,
        'event' => null,
        'reason' => null,
        'body' => null,
        'mediaUrl' => 'url',
        'senderNumber' => 'mobile'
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'contactNumber' => 'contactNumber',
        'date' => 'date',
        'messageId' => 'messageId',
        'event' => 'event',
        'reason' => 'reason',
        'body' => 'body',
        'mediaUrl' => 'mediaUrl',
        'senderNumber' => 'senderNumber'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'contactNumber' => 'setContactNumber',
        'date' => 'setDate',
        'messageId' => 'setMessageId',
        'event' => 'setEvent',
        'reason' => 'setReason',
        'body' => 'setBody',
        'mediaUrl' => 'setMediaUrl',
        'senderNumber' => 'setSenderNumber'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'contactNumber' => 'getContactNumber',
        'date' => 'getDate',
        'messageId' => 'getMessageId',
        'event' => 'getEvent',
        'reason' => 'getReason',
        'body' => 'getBody',
        'mediaUrl' => 'getMediaUrl',
        'senderNumber' => 'getSenderNumber'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    const EVENT_SENT = 'sent';
    const EVENT_DELIVERED = 'delivered';
    const EVENT_READ = 'read';
    const EVENT_ERROR = 'error';
    const EVENT_UNSUBSCRIBE = 'unsubscribe';
    const EVENT_REPLY = 'reply';
    const EVENT_SOFT_BOUNCE = 'soft-bounce';
    

    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getEventAllowableValues()
    {
        return [
            self::EVENT_SENT,
            self::EVENT_DELIVERED,
            self::EVENT_READ,
            self::EVENT_ERROR,
            self::EVENT_UNSUBSCRIBE,
            self::EVENT_REPLY,
            self::EVENT_SOFT_BOUNCE,
        ];
    }
    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['contactNumber'] = isset($data['contactNumber']) ? $data['contactNumber'] : null;
        $this->container['date'] = isset($data['date']) ? $data['date'] : null;
        $this->container['messageId'] = isset($data['messageId']) ? $data['messageId'] : null;
        $this->container['event'] = isset($data['event']) ? $data['event'] : null;
        $this->container['reason'] = isset($data['reason']) ? $data['reason'] : null;
        $this->container['body'] = isset($data['body']) ? $data['body'] : null;
        $this->container['mediaUrl'] = isset($data['mediaUrl']) ? $data['mediaUrl'] : null;
        $this->container['senderNumber'] = isset($data['senderNumber']) ? $data['senderNumber'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['contactNumber'] === null) {
            $invalidProperties[] = "'contactNumber' can't be null";
        }
        if ($this->container['date'] === null) {
            $invalidProperties[] = "'date' can't be null";
        }
        if ($this->container['messageId'] === null) {
            $invalidProperties[] = "'messageId' can't be null";
        }
        if ($this->container['event'] === null) {
            $invalidProperties[] = "'event' can't be null";
        }
        $allowedValues = $this->getEventAllowableValues();
        if (!is_null($this->container['event']) && !in_array($this->container['event'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'event', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        if ($this->container['senderNumber'] === null) {
            $invalidProperties[] = "'senderNumber' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets contactNumber
     *
     * @return string
     */
    public function getContactNumber()
    {
        return $this->container['contactNumber'];
    }

    /**
     * Sets contactNumber
     *
     * @param string $contactNumber WhatsApp Number with country code. Example, 85264318721
     *
     * @return $this
     */
    public function setContactNumber($contactNumber)
    {
        $this->container['contactNumber'] = $contactNumber;

        return $this;
    }

    /**
     * Gets date
     *
     * @return string
     */
    public function getDate()
    {
        return $this->container['date'];
    }

    /**
     * Sets date
     *
     * @param string $date UTC date-time on which the event has been generated
     *
     * @return $this
     */
    public function setDate($date)
    {
        $this->container['date'] = $date;

        return $this;
    }

    /**
     * Gets messageId
     *
     * @return string
     */
    public function getMessageId()
    {
        return $this->container['messageId'];
    }

    /**
     * Sets messageId
     *
     * @param string $messageId Message ID which generated the event
     *
     * @return $this
     */
    public function setMessageId($messageId)
    {
        $this->container['messageId'] = $messageId;

        return $this;
    }

    /**
     * Gets event
     *
     * @return string
     */
    public function getEvent()
    {
        return $this->container['event'];
    }

    /**
     * Sets event
     *
     * @param string $event Event which occurred
     *
     * @return $this
     */
    public function setEvent($event)
    {
        $allowedValues = $this->getEventAllowableValues();
        if (!in_array($event, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'event', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['event'] = $event;

        return $this;
    }

    /**
     * Gets reason
     *
     * @return string
     */
    public function getReason()
    {
        return $this->container['reason'];
    }

    /**
     * Sets reason
     *
     * @param string $reason Reason for the event (will be there in case of `error` and `soft-bounce` events)
     *
     * @return $this
     */
    public function setReason($reason)
    {
        $this->container['reason'] = $reason;

        return $this;
    }

    /**
     * Gets body
     *
     * @return string
     */
    public function getBody()
    {
        return $this->container['body'];
    }

    /**
     * Sets body
     *
     * @param string $body Text of the reply (will be there only in case of `reply` event with text)
     *
     * @return $this
     */
    public function setBody($body)
    {
        $this->container['body'] = $body;

        return $this;
    }

    /**
     * Gets mediaUrl
     *
     * @return string
     */
    public function getMediaUrl()
    {
        return $this->container['mediaUrl'];
    }

    /**
     * Sets mediaUrl
     *
     * @param string $mediaUrl Url of the media reply (will be there only in case of `reply` event with media)
     *
     * @return $this
     */
    public function setMediaUrl($mediaUrl)
    {
        $this->container['mediaUrl'] = $mediaUrl;

        return $this;
    }

    /**
     * Gets senderNumber
     *
     * @return string
     */
    public function getSenderNumber()
    {
        return $this->container['senderNumber'];
    }

    /**
     * Sets senderNumber
     *
     * @param string $senderNumber WhatsApp Number with country code. Example, 85264318721
     *
     * @return $this
     */
    public function setSenderNumber($senderNumber)
    {
        $this->container['senderNumber'] = $senderNumber;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


