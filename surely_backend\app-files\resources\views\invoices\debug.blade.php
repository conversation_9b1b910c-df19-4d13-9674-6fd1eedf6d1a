@extends('layouts.app')

@section('content')
<div class="layout-px-spacing">
    <div class="row layout-top-spacing">
        <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
            <div class="widget-content widget-content-area br-6">
                <div class="row">
                    <div class="col-md-12">
                        <h2>Invoice Debug Information</h2>
                        <div class="alert alert-info">
                            This page shows raw data from the database to help diagnose issues with the invoices display.
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h4>Total Invoices in Database: {{ $totalInvoices }}</h4>
                        <h4>Invoices with Contracts: {{ $invoicesWithContracts }}</h4>
                        <h4>Invoices with Valid Data: {{ count($formattedData) }}</h4>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h4>Sample of First 10 Invoices:</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Contract ID</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sampleInvoices as $invoice)
                                    <tr>
                                        <td>{{ $invoice->id }}</td>
                                        <td>{{ $invoice->contract_id }}</td>
                                        <td>{{ $invoice->type }}</td>
                                        <td>{{ $invoice->amount }}</td>
                                        <td>{{ $invoice->status }}</td>
                                        <td>{{ $invoice->created_at }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h4>Formatted Data for DataTable (First 5):</h4>
                        <pre>{{ json_encode(array_slice($formattedData, 0, 5), JSON_PRETTY_PRINT) }}</pre>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <a href="{{ route('invoices.index') }}" class="btn btn-primary">Back to Invoices</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
