import React from 'react';
import { Tooltip, Actionable } from 'reshaped';

interface InfoTooltipProps {
  text: string;
  size?: 'small' | 'medium';
}

/**
 * InfoTooltip - A standardized tooltip component with a question mark icon
 * 
 * @param {string} text - The tooltip text to display
 * @param {string} size - Size of the tooltip icon (small or medium)
 */
const InfoTooltip: React.FC<InfoTooltipProps> = ({ text, size = 'small' }) => {
  // Determine size classes based on the size prop
  const containerSizeClass = size === 'small' ? 'h-[17px] w-[17px]' : 'h-[20px] w-[20px]';

  return (
    <Tooltip text={text}>
      {(attributes) => (
        <Actionable attributes={attributes} as='div'>
          <div className={`flex ${containerSizeClass} items-center justify-center self-center rounded-full border border-[#8996B8] bg-[#C7CDDB]`}>
            <span 
              className="material-icons-outlined !text-[12px] !leading-none !font-normal text-[#323C58]" 
              style={{ fontSize: '12px', lineHeight: '1' }}
            >
              question_mark
            </span>
          </div>
        </Actionable>
      )}
    </Tooltip>
  );
};

export default InfoTooltip;
