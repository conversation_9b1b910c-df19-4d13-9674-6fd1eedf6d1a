// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Divider, Modal, Popover, Switch } from 'reshaped';
import useSurelyToast from 'src/hooks/useSurelyToast';
import {
  format,
  addMonths,
  subMonths,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  isSameYear,
  isSameMonth,
  isSameDay,
  addDays,
  isBefore,
  parseISO,
  startOfDay,
} from 'date-fns';
import { enGB } from 'date-fns/locale';
import { editPostJob } from 'src/services/jobs';
import CalendarDayGrid from './CalendarDayGrid';
import right from '../../assets/icons/calendaricon/right.svg';
import left from '../../assets/icons/calendaricon/left.svg';
import { HttpClient } from 'src/client/http-client';

const sameDayCheck = (date1, date2) =>
  isSameYear(parseISO(date1?.start), parseISO(date2?.start)) &&
  isSameMonth(parseISO(date1?.start), parseISO(date2?.start)) &&
  isSameDay(parseISO(date1?.start), parseISO(date2?.start));

const CalendarPostJobNew = ({ handleClosePopovers, closePopovers, active, deactivate, handleParentDates }) => {
  const today = new Date();
  const toast = useSurelyToast();
  const [currentMonthStart, setCurrentMonthStart] = useState(startOfMonth(today));
  const [selectedDates, setSelectedDates] = useState([]);
  const [isDateRangeSelected, setIsDateRangeSelected] = useState(false);
  const [rangeStart, setRangeStart] = useState();
  const [rangeEnd, setRangeEnd] = useState();
  const [calendarDays, setCalendarDays] = useState([]);

  useEffect(() => {
    getCalendarDays();
  }, [currentMonthStart]);

  const toggleSwitch = () => {
    if (isDateRangeSelected) {
      setRangeStart();
      setRangeEnd();
    }
    setIsDateRangeSelected((prevState) => !prevState);
  };

  const rangeGroup = { isDateRangeSelected, toggleSwitch, rangeStart, rangeEnd };

  const handleSelectDatesNew = async (dates) => {
    if (dates?.length) {
      setSelectedDates((prevState) => [...prevState, ...dates]);
    }
    if (dates?.start) {
      setSelectedDates((prevState) => [...prevState, dates]);
    }
  };

  const handleRemoveDates = (dates) => {
    if (dates?.length) {
      const filteredDates = selectedDates.filter((date) => {
        return !dates.some((dateToRemove) => {
          // return date.start === dateToRemove.start && date.end === dateToRemove.end;
          return sameDayCheck(date, dateToRemove);
        });
      });
      setSelectedDates(filteredDates);
    }
    if (dates.start && dates.end) {
      const filteredDates = selectedDates.filter((date) => {
        // return !(date.start === dates.start && date.end === dates.end);
        return !sameDayCheck(date, dates);
      });

      setSelectedDates(filteredDates);
    } else {
      toast.showError('Invalid dates format.', '');
    }
  };

  const prevMonth = () => {
    setCurrentMonthStart(subMonths(currentMonthStart, 1));
  };

  const nextMonth = () => {
    setCurrentMonthStart(addMonths(currentMonthStart, 1));
  };

  const getCalendarDays = () => {
    const firstDayOfMonth = startOfMonth(currentMonthStart);

    const lastDayOfMonth = endOfMonth(currentMonthStart);
    const startDate = startOfWeek(firstDayOfMonth, { weekStartsOn: 1 });
    const endDate = endOfWeek(lastDayOfMonth, { weekStartsOn: 1 });

    const calendarDays = [];
    let currentDate = startDate;

    while (currentDate <= endDate) {
      const formattedDate = format(currentDate, "yyyy-MM-dd'T'HH:mm:ss.SSSxxx", { timeZone: 'local' });
      calendarDays.push(formattedDate);
      currentDate = addDays(currentDate, 1);
    }

    setCalendarDays(calendarDays);
    return calendarDays;
  };

  const handleSubmit = () => {
    handleParentDates(selectedDates);
  };

  return (
    <Modal
      active={active}
      onClose={() => {
        setIsDateRangeSelected(false);
        setRangeStart();
        setRangeEnd();
        handleClosePopovers().then(() => deactivate());
      }}
      className='rubik !h-[auto] !w-[924px]'
    >
      <div className=''>
        <div className='mx-4 flex items-center justify-between border-b border-b-[#DFE2EA] py-3'>
          <Text className='rubik text-[24px] font-normal text-[#323C58]'>Job days and shift hours</Text>
          {/* <span
            className='material-icons-outlined cursor-pointer'
            onClick={() => {
              handleClosePopovers().then(() => deactivate());
            }}
          >
            close
          </span> */}
        </div>
        <div className='mt-6 flex flex-col items-start justify-between gap-3 px-4 py-3 md:flex-row lg:items-center lg:gap-0'>
          <div className='flex w-full items-center justify-between lg:w-1/2'>
            <div className='flex items-center justify-between'>
              <Text className='rubik w-full max-w-[156px] text-lg font-normal leading-[28px] text-[#323C58] sm:mr-6 sm:text-[20px]'>
                {format(currentMonthStart, 'MMMM yyyy')}
              </Text>
              <img src={left} className='mr-1 px-3 py-2.5 sm:mr-3' onClick={prevMonth} />
              <img src={right} className='px-3 py-2.5' onClick={nextMonth} />
            </div>
            <div className='flex w-fit items-center gap-1 sm:gap-2'>
              <span className='w-fit text-[13px] leading-5 text-[#383838]'>Range select</span>
              <Switch name='Date Range' className='' onChange={toggleSwitch} checked={isDateRangeSelected} />
            </div>
          </div>
          <div className='flex gap-5'>
            <div className='flex items-center gap-2'>
              <span className='inline-block h-5 w-[25px] grow-0 rounded-md border border-[#D5D4DF] bg-[#F2F3F7]' />
              <span className='text-[13px] leading-5 text-[#383838]'>Past days</span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='inline-block h-5 w-[25px] grow-0 rounded-md border border-[#0B80E7] bg-[#0B80E7]' />
              <span className='text-[13px] leading-5 text-[#383838]'>Days selected</span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='inline-block h-5 w-[25px] grow-0 rounded-md border border-[#D5D4DF]' />
              <span className='text-[13px] leading-5 text-[#383838]'>Days available</span>
            </div>
          </div>
        </div>
        <div className='border-b-[#DFE2EA border-b pb-6'>
          <CalendarDayGrid
            calendarDays={calendarDays}
            selectedDates={selectedDates}
            rangeGroup={rangeGroup}
            currentMonthStart={currentMonthStart}
            handleSelectDates={handleSelectDatesNew}
            handleRemoveDates={handleRemoveDates}
            closePopovers={closePopovers}
            handleClosePopovers={handleClosePopovers}
            readOnly={false}
          />
        </div>
        <div className='mt-6 flex items-center justify-between gap-4'>
          <Button
            onClick={() => {
              handleClosePopovers().then(() => {
                setIsDateRangeSelected(false);
                setRangeStart();
                setRangeEnd();
                deactivate();
              });
            }}
            className='rubik border-neutral h-[48px] w-full rounded-[8px] border !border-[#DFE2EA] !bg-white px-4 py-2
'
          >
            <div className='flex items-center'>
              <span className='material-icons'>keyboard_arrow_left</span>
              <Text className='rubik text-[16px] font-medium leading-[24px]'>Back</Text>
            </div>
          </Button>
          <Button
            onClick={() => {
              handleClosePopovers().then(() => {
                setIsDateRangeSelected(false);
                setRangeStart();
                setRangeEnd();
                handleSubmit();
                deactivate();
              });
            }}
            className='rubik border-neutral h-[48px] w-full rounded-[8px] border !border-[#0B80E7] !bg-[#0B80E7] px-4 py-2
'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] !text-[#ffff]'>Confirm</Text>
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CalendarPostJobNew;
