#!/bin/bash
# deployment.sh - Simple deployment script with cache clearing

# Run it: ./deployment.sh

# Navigate to backend
cd /var/www/html/surely_backend

# Rebuild containers
echo "Rebuilding backend containers..."
docker-compose down
docker-compose up --build -d

# Clear Laravel caches
echo "Clearing Laravel caches..."
docker-compose exec app php artisan config:clear
docker-compose exec app php artisan cache:clear
docker-compose exec app php artisan view:clear
docker-compose exec app php artisan route:clear

# Navigate to frontend
cd /var/www/html/frontend

# Rebuild frontend
echo "Rebuilding frontend containers..."
docker-compose down
docker-compose up --build -d

echo "Deployment complete with cache clearing."