// @ts-nocheck
import React, { useContext } from 'react';
import { Text, View, Button, Modal, Image } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { useNavigate } from 'react-router-dom';
import { deleteUser } from 'src/services/settings';
import { AuthContext } from 'src/context/AuthContext';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css'
import surleyicon from '../../../../assets/icons/surleyicon/surleyicon.png';

interface DeleteAccountModalProps {
  active: boolean;
  deactivate: () => void;
}

const DeleteAccountModal: React.FC<DeleteAccountModalProps> = ({
  active,
  deactivate,
}) => {
  const navigate = useNavigate();
  const { unAuthenticateUser } = useContext(AuthContext);
  const toastSystem = useToastSystem();

  const handleDeleteAccount = () => {
    deleteUser().then((data) => {
      unAuthenticateUser(), navigate('/');
      toastSystem.showSuccess('Account Successfully Deleted', "We're sorry to see you go. If you change your mind, you're always welcome back. Thanks for being part of our community.");
    });
  };
  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='!w-[424px] !h-[auto] p-[24px]'
    >
      <View className='flex flex-col'>
        <View className=''>
        <button
          onClick={deactivate}
          className='flex btn-no-hover items-center justify-end ml-auto p-0'
        >
          <span className='material-icons align-middle text-500'>close</span>
        </button>
          <span className='material-icons-outlined text-[#CB101D] text-[70px]'>
            report_problem
          </span>
        </View>
        <Text className='text-[#1A1A1A] rubik text-[20px] font-normal mt-[10px]'>
          Delete account
        </Text>
        <Text className='text-[#323C58] rubik text-[15px] font-normal leading-5 mt-[3px]'>
          Are you sure you want to delete the account? Your data will be lost.
        </Text>

        <View className='flex flex-row justify-between mt-[20px]'>
          <Button
            variant='outline'
            onClick={deactivate}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] w-[195px] h-[48px] mr-[10px]'
          >
            <Text className='rubik text-[#1A1A1A] font-medium leading-[24px] text-[16px]'>
            No, go back
            </Text>
          </Button>
          <Button
            onClick={handleDeleteAccount}
            className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px]  !bg-[#CB101D] w-[195px] h-[48px]'
          >
            <Text className='rubik text-[16px] text-[#FFFFFF] leading-[24px] font-medium'>
            Delete Account
            </Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default DeleteAccountModal;
