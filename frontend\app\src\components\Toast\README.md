# SurelyToast Component System

A centralized toast notification system for the Surely frontend application.

## Overview

The SurelyToast system provides a consistent way to display toast notifications throughout the application. It includes:

- **SurelyToast**: The main toast component
- **useSurelyToast**: A custom hook for managing toasts
- **Predefined toast types**: Success, Error, Info, and Welcome toasts

## Features

- ✅ Consistent styling matching existing design patterns
- ✅ Configurable title, content, and optional button
- ✅ Auto-hide functionality with customizable timeout
- ✅ Multiple toast support with automatic positioning
- ✅ Close button (X) in top-right corner
- ✅ Optional Surely icon display
- ✅ Responsive design (mobile and desktop)
- ✅ TypeScript support

## Basic Usage

### 1. Import the hook

```tsx
import { useSurelyToast } from 'src/components/Toast';
```

### 2. Use in your component

```tsx
const MyComponent = () => {
  const { showWelcome, showSuccess, showError, renderToasts } = useSurelyToast();

  const handleAction = () => {
    showWelcome(
      'Enhance your chances of being selected by completing your profile.',
      {
        text: 'Complete your profile',
        onClick: () => navigate('/operator-settings')
      }
    );
  };

  return (
    <div>
      <button onClick={handleAction}>Show Toast</button>
      {renderToasts()}
    </div>
  );
};
```

## API Reference

### useSurelyToast Hook

#### Methods

- `show(config: ToastConfig)`: Show a custom toast
- `showSuccess(title, content, button?)`: Show a success toast (auto-hide: 5s)
- `showError(title, content, button?)`: Show an error toast (no auto-hide)
- `showInfo(title, content, button?)`: Show an info toast (auto-hide: 8s)
- `showWelcome(content, button?)`: Show a welcome toast (no auto-hide)
- `hide(id: string)`: Hide a specific toast
- `hideAll()`: Hide all toasts
- `renderToasts()`: Render all active toasts

#### ToastConfig Interface

```tsx
interface ToastConfig {
  title: string;
  content: string;
  button?: {
    text: string;
    onClick: () => void;
  };
  showIcon?: boolean;
  className?: string;
  timeout?: number; // 0 = no auto-hide
}
```

## Examples

### Welcome Toast (Profile Completion)
```tsx
showWelcome(
  'Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile.',
  {
    text: 'Complete your profile',
    onClick: () => navigate('/operator-settings')
  }
);
```

### Welcome Toast (Payment Setup)
```tsx
showWelcome(
  'In order to start hiring, you will need to fill in your card details in the payment settings section.',
  {
    text: 'Proceed to settings',
    onClick: () => navigate('/client-settings-payment', { state: { activeTab: '2' } })
  }
);
```

### Success Toast
```tsx
showSuccess(
  'Congratulations!',
  'Your Job Post is Now Live'
);
```

### Error Toast
```tsx
showError(
  'Error',
  'An error occurred while processing your request. Please try again.'
);
```

### Custom Toast
```tsx
show({
  title: 'Custom Notification',
  content: 'This is a custom message.',
  showIcon: false,
  timeout: 10000,
  button: {
    text: 'Take Action',
    onClick: () => console.log('Action taken')
  }
});
```

## Migration Guide

### Replacing existing toast implementations

#### Before (using useToast from reshaped):
```tsx
const toast = useToast();

toast.show({
  title: 'Welcome to our platform!',
  text: (
    <div className='h-max space-y-4 py-4'>
      <div className='flex gap-3'>
        <Image key='error' src={surleyicon} className='h-[30px] w-[30px]' />
        <h2>Enhance your chances...</h2>
      </div>
      <Button onClick={() => navigate('/settings')}>
        Complete your profile
      </Button>
    </div>
  ),
  timeout: 50000000,
});
```

#### After (using SurelyToast):
```tsx
const { showWelcome } = useSurelyToast();

showWelcome(
  'Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile.',
  {
    text: 'Complete your profile',
    onClick: () => navigate('/settings')
  }
);
```

## Styling

The component uses the existing design system:
- Background: `#1C212BF7` (dark with transparency)
- Text color: `#EFF0F1` (light gray)
- Button: `#30374A` background with white text
- Font: Rubik family
- Responsive breakpoints: `sm:` prefix for desktop styles

## Notes

- The component automatically handles positioning for multiple toasts
- Toast IDs are auto-generated for tracking
- The close button includes proper ARIA labels for accessibility
- All toasts are rendered at the component level where `renderToasts()` is called
