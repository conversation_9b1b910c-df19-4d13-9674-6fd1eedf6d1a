@extends('layouts.app')

@section('head')
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.dataTables.min.css">
@endsection

@section('content')
<div class="layout-px-spacing">
    <div class="row layout-top-spacing">
        <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
            <div class="widget-content widget-content-area br-6">
                <div class="table-responsive mb-4 mt-4">
                    <div class="d-flex justify-content-between mb-4">
                        <h3>All Invoices</h3>
                        <div>
                            <div class="form-row">
                                <div class="form-group col-md-4">
                                    <label for="user_filter">Filter by User</label>
                                    <select id="user_filter" class="form-control">
                                        <option value="">All Users</option>
                                        <!-- Users will be loaded via AJAX -->
                                    </select>
                                </div>
                                <div class="form-group col-md-4">
                                    <label for="date_from">From Date</label>
                                    <input type="date" id="date_from" class="form-control">
                                </div>
                                <div class="form-group col-md-4">
                                    <label for="date_to">To Date</label>
                                    <input type="date" id="date_to" class="form-control">
                                </div>
                            </div>
                            <button id="apply_filters" class="btn btn-primary">Apply Filters</button>
                            <button id="reset_filters" class="btn btn-outline-primary">Reset</button>
                        </div>
                    </div>
                    
                    <table id="invoices-table" class="table table-hover" style="width:100%">
                        <thead>
                            <tr>
                                <th>Invoice ID</th>
                                <th>Contract ID</th>
                                <th>Client</th>
                                <th>Operative</th>
                                <th>Full Amount</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Payment Due</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>

<script>
    $(document).ready(function() {
        // Load users for filter dropdown
        $.ajax({
            url: '{{ route("mobileUsersData") }}',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                var users = response.data;
                var userSelect = $('#user_filter');
                
                $.each(users, function(index, user) {
                    userSelect.append('<option value="' + user.id + '">' + user.name + ' (' + user.email + ')</option>');
                });
            }
        });

        // Initialize DataTable
        var table = $('#invoices-table').DataTable({
            processing: true,
            serverSide: false, // We're handling the data ourselves
            ajax: {
                url: '{{ route("invoices.data") }}',
                data: function(d) {
                    d.user_id = $('#user_filter').val();
                    d.date_from = $('#date_from').val();
                    d.date_to = $('#date_to').val();
                },
                dataSrc: function(json) {
                    // Log the response data to console for debugging
                    console.log('DataTables AJAX Response:', json);
                    
                    // Check if we have data
                    if (!json.data || json.data.length === 0) {
                        console.log('No invoice data returned from server');
                    }
                    
                    return json.data || [];
                },
                error: function(xhr, error, thrown) {
                    console.error('DataTables Ajax Error:', error, thrown);
                    console.error('Response Text:', xhr.responseText);
                    alert('Error loading invoice data. Please check the console for details.');
                }
            },
            // Add responsive features
            responsive: true,
            // Better error handling
            language: {
                processing: '<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>',
                emptyTable: 'No invoices found',
                zeroRecords: 'No matching invoices found'
            },
            columns: [
                { data: 'invoice_id' },
                { data: 'contract_id' },
                { 
                    data: null,
                    render: function(data) {
                        return data.client_name + '<br><small>' + data.client_email + '</small>';
                    }
                },
                { 
                    data: null,
                    render: function(data) {
                        return data.operative_name + '<br><small>' + data.operative_email + '</small>';
                    }
                },
                { 
                    data: 'amount',
                    render: function(data, type, row) {
                        return row.currency + ' ' + parseFloat(data).toFixed(2);
                    }
                },
                { 
                    data: 'contract_start_date',
                    render: function(data) {
                        return data || 'N/A';
                    }
                },
                { 
                    data: 'contract_end_date',
                    render: function(data) {
                        return data || 'N/A';
                    }
                },
                { 
                    data: 'payment_terms',
                    render: function(data) {
                        return data || 'N/A';
                    }
                },
                {
                    data: 'payment_status',
                    render: function(data) {
                        let statusClass = 'secondary';
                        if (data === 'paid') statusClass = 'success';
                        else if (data === 'pending') statusClass = 'warning';
                        else if (data === 'failed') statusClass = 'danger';
                        
                        return '<span class="badge badge-' + statusClass + '">' + data.charAt(0).toUpperCase() + data.slice(1) + '</span>';
                    }
                },
                {
                    data: null,
                    render: function(data) {
                        return '<a href="{{ url("invoices") }}/' + data.invoice_id + '" class="btn btn-primary btn-sm">View Details</a>';
                    }
                }
            ],
            order: [[0, 'desc']]
        });

        // Apply filters
        $('#apply_filters').on('click', function() {
            table.ajax.reload();
        });

        // Reset filters
        $('#reset_filters').on('click', function() {
            $('#user_filter').val('');
            $('#date_from').val('');
            $('#date_to').val('');
            table.ajax.reload();
        });
    });
</script>
@endsection
