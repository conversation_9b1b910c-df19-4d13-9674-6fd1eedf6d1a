// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { Text, View, Button, Divider, Modal, Select } from 'reshaped';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import PhoneInput from 'react-phone-input-2';
import { requestPhoneChange } from 'src/services/settings';
import { useToastSystem } from 'src/context/ToastSystemContext';

interface ChangePhoneModalProps {
  active: boolean;
  deactivate: () => void;
  phoneNumber: any;
}

const ChangePhoneModal: React.FC<ChangePhoneModalProps> = ({ active, deactivate, phoneNumber }) => {
  const toast = useToastSystem();
  const [newPhone, setNewPhone] = useState<string>();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const submitPhoneChangeRequest = async () => {
    const request: any = {
      newPhone,
    };
    try {
      await requestPhoneChange(request);
      toast.showError(
        'Feature Not Available',
        'Phone number change functionality is currently not available. Please contact support for assistance.'
      );
    } catch (error) {
      toast.showError(
        'Feature Not Available',
        'Phone number change functionality is currently not available. Please contact support for assistance.'
      );
    }
  };

  const isSmallScreen = windowWidth <= 1024;

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Change phone number</Text>
        </View>
        <View className='mt-5 flex flex-col gap-1'>
          <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Current phone number</Text>
          <PhoneInput
            country={'gb'}
            inputStyle={{
              width: isSmallScreen ? '100%' : '536px',
              height: '48px',
              color: '#3C455D',
              fontSize: '14px',
            }}
            value={phoneNumber}
          />
        </View>

        <View className='mt-4 flex flex-col gap-1'>
          <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>New phone number</Text>
          <PhoneInput
            country={'gb'}
            inputStyle={{
              width: isSmallScreen ? '100%' : '536px',
              height: '48px',
              color: '#3C455D',
              fontSize: '14px',
            }}
            value={newPhone}
            onChange={(e) => setNewPhone(e)}
          />
        </View>

        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-end'>
          <Button
            icon={() => <span className='material-icons -mt-1'>clear</span>}
            onClick={deactivate}
            className='border-neutralrounded-[8px] mr-[10px] flex h-[48px] w-[180px] items-center justify-center gap-2 border border-[#DFE2EA] !bg-transparent px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#1A1A1A]'>Cancel</Text>
          </Button>
          <Button
            className='flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] !bg-[#0B80E7] px-4 py-2'
            onClick={() => {
              submitPhoneChangeRequest();
              deactivate();
            }}
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Submit</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ChangePhoneModal;
