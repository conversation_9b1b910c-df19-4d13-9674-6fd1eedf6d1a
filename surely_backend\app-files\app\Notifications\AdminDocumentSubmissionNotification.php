<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminDocumentSubmissionNotification extends Notification
{
    use Queueable;

    private $userId;
    private $userName;
    private $submittedDocuments;

    /**
     * Create a new notification instance.
     *
     * @param int $userId
     * @param string $userName
     * @param array $submittedDocuments
     * @return void
     */
    public function __construct($userId, $userName, $submittedDocuments)
    {
        $this->userId = $userId;
        $this->userName = $userName;
        $this->submittedDocuments = $submittedDocuments;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mailMessage = new MailMessage;
        $mailMessage->subject('New Document Submission - User ID: ' . $this->userId);
        $mailMessage->greeting('New Document Submission');
        $mailMessage->line('A user has submitted documents for verification:');
        $mailMessage->line('User ID: ' . $this->userId);
        $mailMessage->line('User Name: ' . $this->userName);
        
        $mailMessage->line('Submitted Documents:');
        foreach ($this->submittedDocuments as $documentName => $documentStatus) {
            $mailMessage->line("- $documentName: $documentStatus");
        }
        
        $mailMessage->line('Please review these documents in the admin panel.');
        
        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'user_id' => $this->userId,
            'user_name' => $this->userName,
            'submitted_documents' => $this->submittedDocuments
        ];
    }
}
