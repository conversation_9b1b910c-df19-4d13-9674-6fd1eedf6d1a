<?php
/**
 * GetWhatsappCampaignsCampaigns
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * GetWhatsappCampaignsCampaigns Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class GetWhatsappCampaignsCampaigns implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'getWhatsappCampaigns_campaigns';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'id' => 'int',
        'campaignName' => 'string',
        'templateId' => 'string',
        'campaignStatus' => 'string',
        'scheduledAt' => 'string',
        'errorReason' => 'string',
        'invalidatedContacts' => 'int',
        'readPercentage' => 'float',
        'stats' => '\Brevo\Client\Model\WhatsappCampStats',
        'createdAt' => 'string',
        'modifiedAt' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'id' => 'int64',
        'campaignName' => null,
        'templateId' => null,
        'campaignStatus' => null,
        'scheduledAt' => null,
        'errorReason' => null,
        'invalidatedContacts' => 'int64',
        'readPercentage' => 'float',
        'stats' => null,
        'createdAt' => null,
        'modifiedAt' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'id' => 'id',
        'campaignName' => 'campaignName',
        'templateId' => 'templateId',
        'campaignStatus' => 'campaignStatus',
        'scheduledAt' => 'scheduledAt',
        'errorReason' => 'errorReason',
        'invalidatedContacts' => 'invalidatedContacts',
        'readPercentage' => 'readPercentage',
        'stats' => 'stats',
        'createdAt' => 'createdAt',
        'modifiedAt' => 'modifiedAt'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'id' => 'setId',
        'campaignName' => 'setCampaignName',
        'templateId' => 'setTemplateId',
        'campaignStatus' => 'setCampaignStatus',
        'scheduledAt' => 'setScheduledAt',
        'errorReason' => 'setErrorReason',
        'invalidatedContacts' => 'setInvalidatedContacts',
        'readPercentage' => 'setReadPercentage',
        'stats' => 'setStats',
        'createdAt' => 'setCreatedAt',
        'modifiedAt' => 'setModifiedAt'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'id' => 'getId',
        'campaignName' => 'getCampaignName',
        'templateId' => 'getTemplateId',
        'campaignStatus' => 'getCampaignStatus',
        'scheduledAt' => 'getScheduledAt',
        'errorReason' => 'getErrorReason',
        'invalidatedContacts' => 'getInvalidatedContacts',
        'readPercentage' => 'getReadPercentage',
        'stats' => 'getStats',
        'createdAt' => 'getCreatedAt',
        'modifiedAt' => 'getModifiedAt'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    const CAMPAIGN_STATUS_DRAFT = 'draft';
    const CAMPAIGN_STATUS_SCHEDULED = 'scheduled';
    const CAMPAIGN_STATUS_PENDING = 'pending';
    const CAMPAIGN_STATUS_APPROVED = 'approved';
    const CAMPAIGN_STATUS_RUNNING = 'running';
    const CAMPAIGN_STATUS_SUSPENDED = 'suspended';
    const CAMPAIGN_STATUS_REJECTED = 'rejected';
    const CAMPAIGN_STATUS_SENT = 'sent';
    

    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getCampaignStatusAllowableValues()
    {
        return [
            self::CAMPAIGN_STATUS_DRAFT,
            self::CAMPAIGN_STATUS_SCHEDULED,
            self::CAMPAIGN_STATUS_PENDING,
            self::CAMPAIGN_STATUS_APPROVED,
            self::CAMPAIGN_STATUS_RUNNING,
            self::CAMPAIGN_STATUS_SUSPENDED,
            self::CAMPAIGN_STATUS_REJECTED,
            self::CAMPAIGN_STATUS_SENT,
        ];
    }
    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['id'] = isset($data['id']) ? $data['id'] : null;
        $this->container['campaignName'] = isset($data['campaignName']) ? $data['campaignName'] : null;
        $this->container['templateId'] = isset($data['templateId']) ? $data['templateId'] : null;
        $this->container['campaignStatus'] = isset($data['campaignStatus']) ? $data['campaignStatus'] : null;
        $this->container['scheduledAt'] = isset($data['scheduledAt']) ? $data['scheduledAt'] : null;
        $this->container['errorReason'] = isset($data['errorReason']) ? $data['errorReason'] : null;
        $this->container['invalidatedContacts'] = isset($data['invalidatedContacts']) ? $data['invalidatedContacts'] : null;
        $this->container['readPercentage'] = isset($data['readPercentage']) ? $data['readPercentage'] : null;
        $this->container['stats'] = isset($data['stats']) ? $data['stats'] : null;
        $this->container['createdAt'] = isset($data['createdAt']) ? $data['createdAt'] : null;
        $this->container['modifiedAt'] = isset($data['modifiedAt']) ? $data['modifiedAt'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['id'] === null) {
            $invalidProperties[] = "'id' can't be null";
        }
        if ($this->container['campaignName'] === null) {
            $invalidProperties[] = "'campaignName' can't be null";
        }
        if ($this->container['templateId'] === null) {
            $invalidProperties[] = "'templateId' can't be null";
        }
        if ($this->container['campaignStatus'] === null) {
            $invalidProperties[] = "'campaignStatus' can't be null";
        }
        $allowedValues = $this->getCampaignStatusAllowableValues();
        if (!is_null($this->container['campaignStatus']) && !in_array($this->container['campaignStatus'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'campaignStatus', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        if ($this->container['scheduledAt'] === null) {
            $invalidProperties[] = "'scheduledAt' can't be null";
        }
        if ($this->container['createdAt'] === null) {
            $invalidProperties[] = "'createdAt' can't be null";
        }
        if ($this->container['modifiedAt'] === null) {
            $invalidProperties[] = "'modifiedAt' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets id
     *
     * @return int
     */
    public function getId()
    {
        return $this->container['id'];
    }

    /**
     * Sets id
     *
     * @param int $id ID of the WhatsApp Campaign
     *
     * @return $this
     */
    public function setId($id)
    {
        $this->container['id'] = $id;

        return $this;
    }

    /**
     * Gets campaignName
     *
     * @return string
     */
    public function getCampaignName()
    {
        return $this->container['campaignName'];
    }

    /**
     * Sets campaignName
     *
     * @param string $campaignName Name of the WhatsApp Campaign
     *
     * @return $this
     */
    public function setCampaignName($campaignName)
    {
        $this->container['campaignName'] = $campaignName;

        return $this;
    }

    /**
     * Gets templateId
     *
     * @return string
     */
    public function getTemplateId()
    {
        return $this->container['templateId'];
    }

    /**
     * Sets templateId
     *
     * @param string $templateId Id of the WhatsApp template
     *
     * @return $this
     */
    public function setTemplateId($templateId)
    {
        $this->container['templateId'] = $templateId;

        return $this;
    }

    /**
     * Gets campaignStatus
     *
     * @return string
     */
    public function getCampaignStatus()
    {
        return $this->container['campaignStatus'];
    }

    /**
     * Sets campaignStatus
     *
     * @param string $campaignStatus Status of the WhatsApp Campaign
     *
     * @return $this
     */
    public function setCampaignStatus($campaignStatus)
    {
        $allowedValues = $this->getCampaignStatusAllowableValues();
        if (!in_array($campaignStatus, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'campaignStatus', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['campaignStatus'] = $campaignStatus;

        return $this;
    }

    /**
     * Gets scheduledAt
     *
     * @return string
     */
    public function getScheduledAt()
    {
        return $this->container['scheduledAt'];
    }

    /**
     * Sets scheduledAt
     *
     * @param string $scheduledAt UTC date-time on which WhatsApp campaign is scheduled. Should be in YYYY-MM-DDTHH:mm:ss.SSSZ format
     *
     * @return $this
     */
    public function setScheduledAt($scheduledAt)
    {
        $this->container['scheduledAt'] = $scheduledAt;

        return $this;
    }

    /**
     * Gets errorReason
     *
     * @return string
     */
    public function getErrorReason()
    {
        return $this->container['errorReason'];
    }

    /**
     * Sets errorReason
     *
     * @param string $errorReason Error reason in the campaign creation
     *
     * @return $this
     */
    public function setErrorReason($errorReason)
    {
        $this->container['errorReason'] = $errorReason;

        return $this;
    }

    /**
     * Gets invalidatedContacts
     *
     * @return int
     */
    public function getInvalidatedContacts()
    {
        return $this->container['invalidatedContacts'];
    }

    /**
     * Sets invalidatedContacts
     *
     * @param int $invalidatedContacts Count of invalidated contacts
     *
     * @return $this
     */
    public function setInvalidatedContacts($invalidatedContacts)
    {
        $this->container['invalidatedContacts'] = $invalidatedContacts;

        return $this;
    }

    /**
     * Gets readPercentage
     *
     * @return float
     */
    public function getReadPercentage()
    {
        return $this->container['readPercentage'];
    }

    /**
     * Sets readPercentage
     *
     * @param float $readPercentage Read percentage of the the WhatsApp campaign created
     *
     * @return $this
     */
    public function setReadPercentage($readPercentage)
    {
        $this->container['readPercentage'] = $readPercentage;

        return $this;
    }

    /**
     * Gets stats
     *
     * @return \Brevo\Client\Model\WhatsappCampStats
     */
    public function getStats()
    {
        return $this->container['stats'];
    }

    /**
     * Sets stats
     *
     * @param \Brevo\Client\Model\WhatsappCampStats $stats stats
     *
     * @return $this
     */
    public function setStats($stats)
    {
        $this->container['stats'] = $stats;

        return $this;
    }

    /**
     * Gets createdAt
     *
     * @return string
     */
    public function getCreatedAt()
    {
        return $this->container['createdAt'];
    }

    /**
     * Sets createdAt
     *
     * @param string $createdAt Creation UTC date-time of the WhatsApp campaign (YYYY-MM-DDTHH:mm:ss.SSSZ)
     *
     * @return $this
     */
    public function setCreatedAt($createdAt)
    {
        $this->container['createdAt'] = $createdAt;

        return $this;
    }

    /**
     * Gets modifiedAt
     *
     * @return string
     */
    public function getModifiedAt()
    {
        return $this->container['modifiedAt'];
    }

    /**
     * Sets modifiedAt
     *
     * @param string $modifiedAt UTC date-time of last modification of the whatsapp template (YYYY-MM-DDTHH:mm:ss.SSSZ)
     *
     * @return $this
     */
    public function setModifiedAt($modifiedAt)
    {
        $this->container['modifiedAt'] = $modifiedAt;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


