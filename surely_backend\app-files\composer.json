{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "arielmejiadev/larapex-charts": "^6.0", "barryvdh/laravel-cors": "^3.0", "cartalyst/stripe-laravel": "^13.1", "doctrine/dbal": "^3.7", "fabpot/goutte": "^4.0", "getbrevo/brevo-php": "^2.0", "google/apiclient": "^2.15", "guzzlehttp/guzzle": "^7.8", "laravel/framework": "^8.75", "laravel/passport": "^10.3", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "laravel/ui": "^3.4", "livewire/livewire": "^2.12", "mcred/detect-credit-card-type": "^0.1.0", "messagebird/php-rest-api": "^3.1", "pusher/pusher-php-server": "^7.2", "stripe/stripe-php": "^16.3", "swiftmailer/swiftmailer": "^6.0", "symfony/dom-crawler": "^6.0", "vlucas/phpdotenv": "^5.5", "yajra/laravel-datatables-buttons": "^4.13", "yajra/laravel-datatables-oracle": "^9.21"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}