<div class="sidebar-wrapper sidebar-theme" id="sidebar-div" aria-expanded="true">


    <nav id="sidebar">

        @if (auth()->user()->role == \App\Models\User::Admin)
        <ul class="navbar-nav theme-brand flex-row  text-center">
            <li class="nav-item ">

                <a href="{{route('home')}}">
                    <!-- <p class="h2 p-3 text-white fw-bold">Surely</p> -->
                    <img src="{{asset('logo.svg')}}" style="width: 100%" class="navbar-logo" alt="logo">
                </a>
            </li>

        </ul>
        <div class="shadow-bottom"></div>
        <ul class="list-unstyled menu-categories" id="accordionExample">


            <li class="menu @if(Request::is('home'))  active @endif ">
                <a href="{{route('home')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-home">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>

                        <span>Dashboard</span>
                    </div>
                </a>
            </li>


            <li class="menu @if(Request::is('users/*'))  active @endif ">
                <a href="{{route('users')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-users">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span>Users</span>
                    </div>
                </a>
            </li>


            <li class="menu">
                <a href="#dashboard" data-toggle="collapse" @if(Request::is('mobile_users/*') || Request::is('email-requests')) aria-expanded="true"
                    class="dropdown-toggle" @else aria-expanded="false" class="dropdown-toggle  collapsed" @endif>
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-users">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span>Mobile users</span>
                    </div>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-chevron-right">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>
                </a>
                <ul class="submenu list-unstyled collapse  @if(Request::is('mobile_users/*') || Request::is('email-requests')) show  @endif"
                    id="dashboard" data-parent="#accordionExample" style="">
                    <li @if(Route::is('mobile_users') && request('account_type') == 0) class="active" @endif>
                        <a href="{{route('mobile_users')}}"> All </a>
                    </li>
                    <li @if(Route::is('mobile_users') && request('account_type') == 1) class="active" @endif>
                        <a href="{{route('mobile_users')}}?account_type=1"> Operatives </a>
                    </li>
                    <li @if(Route::is('mobile_users') && request('account_type') == 2) class="active" @endif>
                        <a href="{{route('mobile_users')}}?account_type=2"> Clients </a>
                    </li>
                    <li @if(Route::is('email_requests_table')) class="active" @endif>
                        <a href="{{route('email_requests_table')}}"> Email Requests </a>
                    </li>
                </ul>
            </li>

            <li class="menu @if(Request::is('newsletter'))  active @endif ">
                <a href="{{route('newsletter_table')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Newsletter Subscribers</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('blogs') || Request::is('blogs/*'))  active @endif ">
                <a href="{{route('blogs.index')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Blogs</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('supports') || Request::is('supports/*'))  active @endif ">
                <a href="{{route('supports_table')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Ask for Support</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('reviews') || Request::is('reviews/*'))  active @endif ">
                <a href="{{route('reviews_table')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Reviews</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('reports') || Request::is('reports/*'))  active @endif ">
                <a href="{{route('reports_table')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Reports</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('jobs'))  active @endif ">
                <a href="{{route('jobs_table')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Jobs</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('payments'))  active @endif ">
                <a href="{{route('payment')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Payments</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('invoices') || Request::is('invoices/*'))  active @endif ">
                <a href="{{route('invoices.index')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file-text">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                        </svg>
                        <span>Invoices</span>
                    </div>
                </a>
            </li>
        </ul>
        @endif

        @if (auth()->user()->role == \App\Models\User::Marketing)
        <ul class="navbar-nav theme-brand flex-row  text-center">
            <li class="nav-item ">

                <a href="{{route('home')}}">
                    <!-- <p class="h2 p-3 text-white fw-bold">Surely</p> -->
                    <img src="{{asset('logo.svg')}}" style="width: 100%" class="navbar-logo" alt="logo">
                </a>
            </li>

        </ul>
        <div class="shadow-bottom"></div>

        <ul class="list-unstyled menu-categories" id="accordionExample">
            <li class="menu @if(Request::is('home'))  active @endif ">
                <a href="{{route('home')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-home">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>

                        <span>Dashboard</span>
                    </div>
                </a>
            </li>
            
            <li class="menu @if(Request::is('blogs') || Request::is('blogs/*'))  active @endif ">
                <a href="{{route('blogs.index')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Blogs</span>
                    </div>
                </a>
            </li>

            <li class="menu @if(Request::is('jobs'))  active @endif ">
                <a href="{{route('jobs_table')}}" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-layers">
                            <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                            <polyline points="2 17 12 22 22 17"></polyline>
                            <polyline points="2 12 12 17 22 12"></polyline>
                        </svg>
                        <span>Jobs</span>
                    </div>
                </a>
            </li>
        </ul>
        @endif

    </nav>

</div>