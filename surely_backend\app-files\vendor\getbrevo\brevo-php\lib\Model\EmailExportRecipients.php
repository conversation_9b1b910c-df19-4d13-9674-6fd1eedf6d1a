<?php
/**
 * EmailExportRecipients
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * EmailExportRecipients Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class EmailExportRecipients implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'emailExportRecipients';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'notifyURL' => 'string',
        'recipientsType' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'notifyURL' => 'url',
        'recipientsType' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'notifyURL' => 'notifyURL',
        'recipientsType' => 'recipientsType'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'notifyURL' => 'setNotifyURL',
        'recipientsType' => 'setRecipientsType'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'notifyURL' => 'getNotifyURL',
        'recipientsType' => 'getRecipientsType'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    const RECIPIENTS_TYPE_ALL = 'all';
    const RECIPIENTS_TYPE_NON_CLICKERS = 'nonClickers';
    const RECIPIENTS_TYPE_NON_OPENERS = 'nonOpeners';
    const RECIPIENTS_TYPE_CLICKERS = 'clickers';
    const RECIPIENTS_TYPE_OPENERS = 'openers';
    const RECIPIENTS_TYPE_SOFT_BOUNCES = 'softBounces';
    const RECIPIENTS_TYPE_HARD_BOUNCES = 'hardBounces';
    const RECIPIENTS_TYPE_UNSUBSCRIBED = 'unsubscribed';
    

    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getRecipientsTypeAllowableValues()
    {
        return [
            self::RECIPIENTS_TYPE_ALL,
            self::RECIPIENTS_TYPE_NON_CLICKERS,
            self::RECIPIENTS_TYPE_NON_OPENERS,
            self::RECIPIENTS_TYPE_CLICKERS,
            self::RECIPIENTS_TYPE_OPENERS,
            self::RECIPIENTS_TYPE_SOFT_BOUNCES,
            self::RECIPIENTS_TYPE_HARD_BOUNCES,
            self::RECIPIENTS_TYPE_UNSUBSCRIBED,
        ];
    }
    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['notifyURL'] = isset($data['notifyURL']) ? $data['notifyURL'] : null;
        $this->container['recipientsType'] = isset($data['recipientsType']) ? $data['recipientsType'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['recipientsType'] === null) {
            $invalidProperties[] = "'recipientsType' can't be null";
        }
        $allowedValues = $this->getRecipientsTypeAllowableValues();
        if (!is_null($this->container['recipientsType']) && !in_array($this->container['recipientsType'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'recipientsType', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets notifyURL
     *
     * @return string
     */
    public function getNotifyURL()
    {
        return $this->container['notifyURL'];
    }

    /**
     * Sets notifyURL
     *
     * @param string $notifyURL Webhook called once the export process is finished. For reference, https://help.brevo.com/hc/en-us/articles/360007666479
     *
     * @return $this
     */
    public function setNotifyURL($notifyURL)
    {
        $this->container['notifyURL'] = $notifyURL;

        return $this;
    }

    /**
     * Gets recipientsType
     *
     * @return string
     */
    public function getRecipientsType()
    {
        return $this->container['recipientsType'];
    }

    /**
     * Sets recipientsType
     *
     * @param string $recipientsType Type of recipients to export for a campaign
     *
     * @return $this
     */
    public function setRecipientsType($recipientsType)
    {
        $allowedValues = $this->getRecipientsTypeAllowableValues();
        if (!in_array($recipientsType, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'recipientsType', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['recipientsType'] = $recipientsType;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


