// @ts-nocheck
import React from 'react';
import { View, Text, Button, Image } from 'reshaped';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

export interface SurelyToastProps {
  /** The title of the toast */
  title: string;
  /** The main content/message of the toast */
  content: string;
  /** Optional button configuration */
  button?: {
    text: string;
    onClick: () => void;
  };
  /** Function to call when the close button is clicked */
  onClose: () => void;
  /** Whether to show the toast */
  isVisible?: boolean;
  /** Additional CSS classes for positioning */
  className?: string;
  /** Whether to show the Surely icon */
  showIcon?: boolean;
  /** If true, the toast positions itself fixed to the viewport; if false, it renders inline */
  fixed?: boolean;
}

const SurelyToast: React.FC<SurelyToastProps> = ({
  title,
  content,
  button,
  onClose,
  isVisible = true,
  className = '',
  showIcon = true,
  fixed = true,
}) => {
  if (!isVisible) {
    return null;
  }

  const Card = (
    <View className='w-[370px] sm:w-[420px] bg-[#1C212BF7] rounded-[8px] text-white'>
      {/* Close Button */}
      <button
        className='ml-auto flex items-center justify-end border border-transparent bg-transparent hover:border-transparent p-2'
        onClick={onClose}
        aria-label="Close toast"
      >
        <span className='material-icons text-500 align-middle text-[#fff]'>
          close
        </span>
      </button>

      {/* Content */}
      <View className={`${showIcon ? 'ml-5 mr-4 mt-[-12px] flex items-start gap-4' : 'mx-5 mt-[-12px]'}`}>
        {showIcon && (
          <Image
            src={surleyicon}
            alt='Surely Icon'
            className='w-[64px] h-[64px] flex-shrink-0'
          />
        )}

        <View className={`text-left ${button ? 'mb-[20px]' : 'mb-[45px]'} ${showIcon ? 'w-[308px]' : 'w-full'}`}>
          {/* Title */}
          <Text className='text-[#EFF0F1] rubik text-[14px] leading-[20px] font-bold'>
            {title}
          </Text>

          {/* Content */}
          <Text className='text-[#EFF0F1] rubik text-[14px] font-normal leading-[20px] mt-1'>
            {content}
          </Text>

          {/* Button */}
          {button && (
            <Button
              className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
              onClick={button.onClick}
            >
              {button.text}
            </Button>
          )}
        </View>
      </View>
    </View>
  );

  if (!fixed) {
    return <div className={`w-[320px] sm:w-[420px] ${className}`}>{Card}</div>;
  }

  return (
    <View className={`fixed bottom-1 sm:bottom-7 right-[15.3%] sm:right-5 transform translate-y-0 w-[320px] sm:w-[420px] mt-[26px] z-10 ${className}`}>
      {Card}
    </View>
  );
};

export default SurelyToast;
