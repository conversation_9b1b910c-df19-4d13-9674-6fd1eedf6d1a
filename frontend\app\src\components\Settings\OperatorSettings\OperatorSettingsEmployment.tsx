// @ts-nocheck
import React, { useState, useMemo, useEffect, useContext } from 'react';
import { Text, View, Button, Tabs, Divider, useToggle, Image, Breadcrumbs } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { useNavigate, useLocation } from 'react-router-dom';
import AddPositionEmployment from './ModalsOperatorSettings/AddPositionEmployment';
import AddQualificationEmployment from './ModalsOperatorSettings/AddQualificationEmployment';
import AddCVEmployment from './ModalsOperatorSettings/AddCVEmployment';
import { getPosition, addPosition } from 'src/services/settings';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { headerLogo } from 'src/assets/images';
import { AppContext } from 'src/context/AppContext';
import moment from 'moment';
import employmentIcon from '../../../assets/icons/employmentSettingsIcon.svg';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';

const OperatorSettingsEmployment: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const toastSystem = useToastSystem();
  const { fetchAppData } = useContext(AppContext);
  const [isEditing, setIsEditing] = useState(false);
  const [isEditingQualification, setIsEditingQualification] = useState(false);
  const redirectActiveTab = useMemo(() => location?.state?.activeTab, []);

  const [activeTab, setActiveTab] = useState(redirectActiveTab || '0');
  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const { active: active2, activate: activate2, deactivate: deactivate2 } = useToggle(false);
  const { active: active3, activate: activate3, deactivate: deactivate3 } = useToggle(false);

  const [positionArray, setPositionArray] = useState<any>([]);
  const [qualificationArray, setQualificationArray] = useState<any>([]);
  const [curiculumVitae, setCuriculumVitae] = useState<any>();
  const [curiculumVitaeName, setCuriculumVitaeName] = useState<any>();
  const [selectedPosition, setSelectedPosition] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedQualification, setselectedQualification] = useState<any>(null);
  useEffect(() => {
    getPosition().then((data: any) => {
      setCuriculumVitae(data.cv.cv);
      setPositionArray(data.employments);
      setQualificationArray(data.qualifications);
      setCuriculumVitaeName(data.cv.cv_name);

    });
  }, []);

  const handleAddPosition = (newPositionData: any) => {
    const updatedPositionArray = [...positionArray, { ...newPositionData, id: Date.now() }];
    setPositionArray(updatedPositionArray);
  };

  const handleEditPosition = ({ id, data }: { id: any; data: any }) => {
    const positionIndex = positionArray.findIndex((position: any) => position.id === id);
    if (positionIndex !== -1) {
      const updatedPositionArray = [...positionArray];
      updatedPositionArray[positionIndex] = { id, ...data };
      setPositionArray(updatedPositionArray);
    }
  };

  const handleAddCV = (newCVData: any) => {
    setCuriculumVitae(newCVData.selectedFiles);
    setCuriculumVitaeName(newCVData.fileName);
  };

  const handleAddQualification = (newQualificationData: any) => {
    const updatedQualificationArray = [...qualificationArray, { ...newQualificationData, id: Date.now() }];
    setQualificationArray(updatedQualificationArray);
  };

  const handleEditQualification = ({ id, data }: { id: any; data: any }) => {
    const qualificationIndex = qualificationArray.findIndex((position: any) => position.id === id);
    if (qualificationIndex !== -1) {
      const updatedQualificationArray = [...qualificationArray];
      updatedQualificationArray[qualificationIndex] = { id, ...data };
      setQualificationArray(updatedQualificationArray);
    }
  };

  const handleDeletePosition = (id: any) => {
    const updatedPositionArray = positionArray.filter((item: any) => item.id !== id);
    setPositionArray(updatedPositionArray);
  };

  const handleDeleteQualification = (id: any) => {
    const updatedQualificationArray = qualificationArray.filter((item: any) => item.id !== id);
    setQualificationArray(updatedQualificationArray);
  };

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    await addPosition({
      employment: positionArray,
      qualifications: qualificationArray,
      cv: { cv: curiculumVitae, cv_name: curiculumVitaeName },
    })
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
      });
  };

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  return (
    <View className='  sm:w-auto mx-auto px-[12px] md:px-0 overflow-hidden'>
      <Breadcrumbs className='mb-[20px] text-base'>
        <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
          <span className='text-[#3C455D] rubik text-[16px]'>Profile</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
          <span className='text-[#3C455D] rubik text-[16px]'>Account settings</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='font-medium rubik text-[#1A1A1A] text-[16px]'>Employment</span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='flex items-center p-0 mb-[16px] lg:mb-5'>
        <Text className='text-foreground-neutral lg:text-[32px] font-rufina-stencil xl:leading-10 text-[#323C58]'>
          Employment
        </Text>
      </View>
      <View className='font-normal rubik text-[#14171F]'>
        <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
          <Tabs.List>
            <Tabs.Item value='0'>Employment history</Tabs.Item>
            <Tabs.Item value='1'>Relevant qualifications</Tabs.Item>
          </Tabs.List>
        </Tabs>
      </View>
      {activeTab === '0' && (
        <View className='flex flex-col-reverse lg:flex-row justify-between'>
          <View className='flex flex-col mt-[16px]'>
            <View className='flex flex-row '>
              <Button
                icon={() => <span className='material-icons-outlined flex items-center text-[#0B80E7]'>add</span>}
                onClick={() => {
                  setIsEditing(false);
                  setSelectedPosition(null);
                  setTimeout(() => {
                    activate1();
                  }, 250);
                }}
                className='btn-no-hover !bg-transparent w-[160px] flex justify-start h-[3px] mt-[12px]'
              >
                <span className='align-middle text-base	font-medium leading-6 rubik text-[#323C58]'>Add position</span>
              </Button>

              <AddPositionEmployment
                active={active1}
                deactivate={deactivate1}
                positionArray={positionArray}
                onAddPosition={handleAddPosition}
                selectedPosition={selectedPosition}
                onEditPosition={handleEditPosition}
                isEditing={isEditing}
              />

              <Button
                icon={() => (
                  <span className='w-[22px] material-icons-outlined flex items-center text-[#0B80E7]'>upload_2</span>
                )}
                onClick={activate2}
                className='btn-no-hover !bg-transparent flex h-[32px] mt-[12px]'
              >
                <span className='align-middle text-base	font-medium leading-6 rubik text-[#323C58]'>Upload CV</span>
              </Button>
              <AddCVEmployment
                active={active2}
                deactivate={deactivate2}
                curiculumVitae={curiculumVitae}
                addCuriculumVitae={handleAddCV}
                curiculumVitaeName={curiculumVitaeName}
              />
            </View>
            <View className=''>
              {positionArray.map(({ id, jobTitle, startDate, endDate }: any) => {
                let formattedStartDate = moment(startDate).format('DD.MM.YYYY');
                let formattedEndDate;
                endDate ? (formattedEndDate = moment(endDate).format('DD.MM.YYYY')) : (formattedEndDate = null);
                return (
                  <View key={id} className='flex items-center justify-between '>
                    <div className='flex flex-col py-1'>
                      <Text className='text-[15px] font-normal leading-5 rubik !text-[#1A1A1A]'>
                        {formattedStartDate} - {formattedEndDate ?? 'Now'}
                      </Text>
                      <Text className='text-[14px] font-normal leading-5 rubik !text-[#14171F]'>{jobTitle}</Text>
                    </div>
                    <div className='flex flex-row gap-[5px]'>
                      <Button
                        variant='outline'
                        onClick={() => {
                          setIsEditing(true);
                          const selectedPositionData = positionArray.find((position: any) => position.id === id);
                          setSelectedPosition(selectedPositionData);
                          setTimeout(() => {
                            activate1();
                          }, 250);
                        }}
                        className='flex items-center justify-center w-[78px] mt-[10px]  !bg-[#fff] !border-[#DFE2EA]  '
                      >
                        <Text className='flex align-middle items-center text-[#0B80E7] text-sm font-normal p-[4px 8px 4px 8px]'>
                          <span className='material-icons align-middle text-[18px] mr-[5px]'>edit</span>
                          Edit
                        </Text>
                      </Button>
                      <Button
                        variant='outline'
                        className='flex items-center justify-center w-[78px] mt-[10px] ml-[16px] !bg-[#fff] !border-[#DFE2EA] '
                        onClick={() => handleDeletePosition(id)}
                      >
                        <Text className='flex align-middle items-center text-[#CB101D] text-sm font-normal p-[4px 8px 4px 8px]'>
                          <span className='material-icons align-middle text-[18px]'>close</span>
                          Delete
                        </Text>
                      </Button>
                    </div>
                  </View>
                );
              })}
            </View>
            {curiculumVitae && (
              <>
                <Divider className='w-full h-[1px] mt-[16px]'></Divider>
                <View className='flex flex-row mt-[10px] justify-between	'>
                  <View className='flex flex-col'>
                    <Text className='mt-[10px] text-gray-300 text-[15px] font-normal leading-5 rubik !text-[#383838]'>
                      CV uploaded
                    </Text>
                    <Text className=''>{curiculumVitaeName}</Text>
                  </View>
                  <Button
                    variant='outline'
                    className='flex items-center justify-center w-[78px] mt-[10px] ml-[16px] !bg-[#fff] !border-[#DFE2EA] '
                    onClick={() => setCuriculumVitae(null)}
                  >
                    <Text className='flex align-middle items-center text-[#CB101D] text-sm font-normal p-[4px 8px 4px 8px]'>
                      <span className='material-icons align-middle text-[18px]'>close</span>
                      Delete
                    </Text>
                  </Button>
                </View>
              </>
            )}
            <Divider className='w-full h-[1px] mt-[16px]'></Divider>

            <View className='flex flex-row gap-2 justify-between mt-[16px]'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] !bg-[#fff] rubik text-[16px] font-medium !border-[#DFE2EA] sm:w-[260px] h-[48px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleSaveSettings().then(() => {
                    toastSystem.showSuccess('Done!', 'You have updated your profile');
                  })
                }}
                className={`flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] rubik text-[16px] font-medium ${
                  isSaving ? '!text-[#0B80E7] !bg-[#fff] !border-[#0B80E7]' : '!text-[#ffff] !bg-[#0B80E7]'
                } sm:w-[260px] h-[48px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='flex flex-col sm:w-[313px] md:w-[530px] lg:w-[313px] gap-[14px] ml-[0px] lg:ml-[135px] mt-[15px] lg:mt-[0px]'>
            <Image src={headerLogo} className='w-[109.76px] h-[41.274px] flex-shrink-0 ml-[-5px]' />
            <Text className='text-[#3C455D] rubik text-[14px] font-normal leading-[20px] '>
              The Employment History section allows you to showcase your professional experience as a security
              operative. Include accurate and relevant details to provide a comprehensive overview of your work history.
            </Text>
          </View>
        </View>
      )}
      {activeTab === '1' && (
        <View className='flex flex-col-reverse lg:flex-row justify-between'>
          <View className='flex flex-col mt-[16px]'>
            <View className='flex flex-col '>
              <Button
                icon={() => <span className='material-icons-outlined flex items-center text-[#0B80E7]'>add</span>}
                onClick={() => {
                  setIsEditingQualification(false);
                  setselectedQualification(null);
                  setTimeout(() => {
                    activate3();
                  }, 250);
                }}
                className='btn-no-hover !bg-transparent w-[160px] flex justify-start h-[3px] mt-[12px]'
              >
                <span className='align-middle text-base	font-medium leading-6 rubik text-[#323C58]'>
                  Add qualification
                </span>
              </Button>

              <AddQualificationEmployment
                active={active3}
                deactivate={deactivate3}
                qualificationArray={qualificationArray}
                addQualification={handleAddQualification}
                isEditingQualification={isEditingQualification}
                selectedQualification={selectedQualification}
                onEditQualification={handleEditQualification}
              />

              {qualificationArray.map(({ id, qualificationTitle, qualificationDescription, expiryDate }: any) => {
                let formattedExpiryDate;
                expiryDate
                  ? (formattedExpiryDate = moment(expiryDate).format('DD.MM.YYYY'))
                  : (formattedExpiryDate = null);
                return (
                  <View key={id} className='flex items-center justify-between '>
                    <div className='flex flex-col py-1'>
                      <Text className='text-[15px] font-normal leading-5 rubik !text-[#1A1A1A]'>
                        {qualificationTitle}
                      </Text>
                      <Text className='text-[14px] font-normal leading-5 rubik !text-[#14171F]'>
                        {qualificationDescription}
                      </Text>
                      <Text className='text-[14px] font-normal leading-5 rubik !text-[#383838]'>
                        Expiry date: {formattedExpiryDate}
                      </Text>
                    </div>
                    <div className='flex flex-row gap-[5px]'>
                      <Button
                        variant='outline'
                        onClick={() => {
                          setIsEditingQualification(true);
                          const selectedQualificationData = qualificationArray.find(
                            (qualification: any) => qualification.id === id,
                          );
                          setselectedQualification(selectedQualificationData);
                          setTimeout(() => {
                            activate3();
                          }, 250);
                        }}
                        className='flex items-center justify-center w-[78px] mt-[10px]  !bg-[#fff] !border-[#DFE2EA]  '
                      >
                        <Text className='flex align-middle items-center text-[#0B80E7] text-sm font-normal p-[4px 8px 4px 8px]'>
                          <span className='material-icons align-middle text-[18px] mr-[5px]'>edit</span>
                          Edit
                        </Text>
                      </Button>
                      <Button
                        variant='outline'
                        className='flex items-center justify-center w-[78px] mt-[10px] ml-[16px] !bg-[#fff] !border-[#DFE2EA] '
                        onClick={() => handleDeleteQualification(id)}
                      >
                        <Text className='flex align-middle items-center text-[#CB101D] text-sm font-normal p-[4px 8px 4px 8px]'>
                          <span className='material-icons align-middle text-[18px]'>close</span>
                          Delete
                        </Text>
                      </Button>
                    </div>
                  </View>
                );
              })}
            </View>
            <Divider className='w-full h-[1px] mt-[16px]'></Divider>

            <View className='flex flex-row gap-2 justify-between mt-[16px]'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] !bg-[#fff] rubik text-[16px] font-medium !border-[#DFE2EA] sm:w-[260px] h-[48px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleSaveSettings().then(() => {
                    toastSystem.showSuccess('Done!', 'You have updated your profile');
                  })
                }}
                className={`flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded-[8px] rubik text-[16px] font-medium ${
                  isSaving ? '!text-[#0B80E7] !bg-[#fff] !border-[#0B80E7]' : '!text-[#ffff] !bg-[#0B80E7]'
                } sm:w-[260px] h-[48px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='flex flex-col sm:w-[313px] md:w-[530px] lg:w-[313px] gap-[14px] ml-[0px] lg:ml-[135px] mt-[15px] lg:mt-[0px]'>
            <Image src={headerLogo} className='w-[109.76px] h-[41.274px] flex-shrink-0 ml-[-5px]' />
            <Text className='text-[#3C455D] rubik text-[14px] font-normal leading-[20px] '>
              The Employment History section allows you to showcase your professional experience as a security
              operative. Include accurate and relevant details to provide a comprehensive overview of your work history.
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default OperatorSettingsEmployment;
