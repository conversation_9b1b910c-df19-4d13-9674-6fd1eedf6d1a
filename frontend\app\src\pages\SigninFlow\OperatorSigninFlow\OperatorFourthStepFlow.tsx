// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Button, Text, View, Image, useToggle } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { useNavigate } from 'react-router-dom';

import { useRegistrationContext } from 'src/context/RegistrationContext';

import { validateOperatorProfile } from 'src/services/user';
import { headerLogo } from '../../../assets/images';
import { useModalAction } from 'src/context/ModalContext';
import { AppContext } from 'src/context/AppContext';
import CloseAccountCreatorModal from '../CloseAccountCreatorModal/CloseAccountCreatorModal';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const OperatorFourthStepFlow: React.FC = () => {
  const { openModal } = useModalAction();
  const { fetchAppData } = useContext(AppContext);
  const { active, activate, deactivate } = useToggle(false);
  const toastSystem = useToastSystem();

  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImageSelfie, setCapturedImageSelfie] = useState<string | null>(null);
  const navigate = useNavigate();

  const [validSelfie, setValidSelfie] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { sharedRegisterData, operatorRegisterData, setOperatorRegisterData } = useRegistrationContext();

  const openCamera = async () => {
    try {
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
      });
      setStream(cameraStream);
      setIsCameraOpen(true);
    } catch (error) {
      // console.error('Error accessing camera:', error);
    }
  };

  const closeCamera = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    setStream(null);
    setIsCameraOpen(false);
  };

  const takePicture = () => {
    if (stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      const canvas = document.createElement('canvas');
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      const context = canvas.getContext('2d');
      if (context) {
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const capturedDataURL = canvas.toDataURL('image/jpeg');
        setCapturedImageSelfie(capturedDataURL);
        const id1 = toast.show({
          title: 'Picture Saved Successfully:',
          text: 'Your picture has been saved successfully.',
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          startSlot: <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id1)}><Text className='text-[12px]'>X</Text></Button>,        });
      }
      setValidSelfie(true);
    }
  };

  useEffect(() => {
    if (isCameraOpen && stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      videoElement.srcObject = stream;
    }
  }, [isCameraOpen, stream]);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleSubmit = async () => {
    // Start submission tracking with timestamp
    const submissionStartTime = Date.now();
    const submissionId = `submission_${submissionStartTime}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Log submission attempt with detailed information
    console.info('Document submission process started', {
      submissionId,
      timestamp: new Date().toISOString(),
      user: {
        email: sharedRegisterData?.baseData?.email || 'unknown',
        name: `${sharedRegisterData?.baseData?.firstName || ''} ${sharedRegisterData?.baseData?.lastName || ''}`.trim() || 'unknown'
      },
      networkStatus: {
        online: navigator.onLine,
        connectionType: (navigator as any).connection ? (navigator as any).connection.effectiveType : 'unknown'
      }
    });
    
    // Update local state with selfie data
    setOperatorRegisterData((prevState: any) => ({
      ...prevState,
      fourthStep: {
        capturedImageSelfie: capturedImageSelfie,
      },
    }));

    // Validate selfie presence
    if (!capturedImageSelfie) {
      console.warn('Document submission validation failed: Missing selfie', { submissionId });
      return setValidSelfie(false);
    } else setValidSelfie(true);

    // Prepare final data for submission
    const baseData = sharedRegisterData.baseData;
    const crucialData = sharedRegisterData.crucialData;

    const operatorRegisterFinalData = {
      ...baseData,
      ...crucialData,
      ...operatorRegisterData.firstStep,
      ...operatorRegisterData.secondStep,
      ...operatorRegisterData.thirdStep,
      capturedImageSelfie,
      userId: baseData?.userId || localStorage.getItem('user_id') || 'unknown', // Include user ID for tracking
      submissionId // Include submission ID for correlation with backend logs
    };

    // Log document details being submitted
    console.info('Document submission details', {
      submissionId,
      timestamp: new Date().toISOString(),
      documentTypes: {
        siaLicence: operatorRegisterFinalData.capturedImage ? 'present' : 'missing',
        idFront: operatorRegisterFinalData.frontImage ? 'present' : 'missing',
        idBack: operatorRegisterFinalData.backImage ? 'present' : 'missing',
        addressVerification: operatorRegisterFinalData.addressVerificationDocument ? 'present' : 'missing',
        selfie: operatorRegisterFinalData.capturedImageSelfie ? 'present' : 'missing',
      }
    });

    setIsSubmitting(true);
    
    try {
      // Check network connectivity before attempting submission
      if (!navigator.onLine) {
        throw new Error('You appear to be offline. Please check your internet connection and try again.');
      }
      
      console.info(`Document submission API call starting - Submission ID: ${submissionId}`);
      const result = await validateOperatorProfile(operatorRegisterFinalData);
      console.info(`Document submission API call completed - Submission ID: ${submissionId}`);

      setIsSubmitting(false);
      
      if (result && result.success) {
        // Log successful submission
        console.info('Document submission succeeded', {
          submissionId,
          requestId: result.requestId,
          timestamp: new Date().toISOString(),
          processingTime: Date.now() - submissionStartTime,
        });
        
        closeCamera();
        fetchAppData();
        navigate('/my-profile');
      } else {
        // Handle API response errors
        let errorTitle = 'Verification Failed';
        let errorMessage = result?.error || 'Unable to complete profile verification. Please try again.';
        
        if (result?.errorType === 'file_size_error') {
          errorTitle = 'File Size Error';
          errorMessage = 'One or more of your uploaded files is too large. Please try again with a smaller file size.';
        } else if (result?.errorType === 'file_format_error') {
          errorTitle = 'File Format Error';
          errorMessage = 'One or more of your uploaded files has an unsupported format. Supported formats include JPEG, PNG, PDF, and HEIC.';
        } else if (result?.errorType === 'timeout_error') {
          errorTitle = 'Connection Timeout';
          errorMessage = 'The request timed out. Please check your internet connection and try again.';
        } else if (result?.errorType === 'network_error') {
          errorTitle = 'Network Error';
          errorMessage = 'A network error occurred. Please check your internet connection and try again.';
        } else if (result?.errorType === 'aborted_error') {
          errorTitle = 'Request Aborted';
          errorMessage = 'The submission was interrupted. Please try again.';
        } else if (result?.errorType === 'cors_error') {
          errorTitle = 'Security Error';
          errorMessage = 'A security policy prevented the submission. Please try again or contact support.';
        }
        
        // Show user-friendly error toast
        const id = toast.show({
          title: errorTitle,
          text: errorMessage,
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          startSlot: <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id)}><Text className='text-[12px]'>X</Text></Button>,
        });
        
        // Log detailed error information
        console.error('Document submission failed', {
          submissionId,
          requestId: result?.requestId,
          timestamp: new Date().toISOString(),
          processingTime: Date.now() - submissionStartTime,
          errorType: result?.errorType || 'unknown_error',
          errorMessage: result?.error || 'Unknown error',
          networkStatus: {
            online: navigator.onLine,
            connectionType: (navigator as any).connection ? (navigator as any).connection.effectiveType : 'unknown'
          }
        });
        
        // Try to send error telemetry if available
        try {
          if (typeof fetch === 'function' && navigator.onLine) {
            fetch('/api/telemetry/submission-ui-error', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                event: 'submission_ui_error',
                data: {
                  submissionId,
                  requestId: result?.requestId,
                  errorType: result?.errorType || 'unknown_error',
                  errorMessage: result?.error || 'Unknown error',
                  timestamp: new Date().toISOString()
                }
              }),
              keepalive: true
            }).catch(e => console.warn('Failed to send UI error telemetry', e));
          }
        } catch (e) {
          // Silently fail telemetry
          console.warn('UI error telemetry failed:', e);
        }
      }
    } catch (error) {
      // Handle unexpected errors during submission process
      setIsSubmitting(false);
      
      // Determine error type for user-friendly message
      let errorTitle = 'Submission Error';
      let errorMessage = 'An unexpected error occurred during submission. Please try again.';
      let errorType = 'unexpected_error';
      
      if (error instanceof Error) {
        if (error.message.includes('offline')) {
          errorTitle = 'Network Error';
          errorMessage = error.message;
          errorType = 'offline_error';
        } else if (error.message.includes('timeout')) {
          errorTitle = 'Connection Timeout';
          errorMessage = 'The request timed out. Please try again later.';
          errorType = 'timeout_error';
        }
      }
      
      // Show user-friendly error toast
      toastSystem.showError(errorTitle, errorMessage);
      
      // Log detailed error information
      console.error('Document submission exception', {
        submissionId,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - submissionStartTime,
        errorType,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        networkStatus: {
          online: navigator.onLine,
          connectionType: (navigator as any).connection ? (navigator as any).connection.effectiveType : 'unknown'
        }
      });
    }
  };

  return (
    <View className='mt-[20px] flex flex-col overflow-hidden px-[12px] sm:mt-[84.3px] md:px-0'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button variant='ghost' onClick={activate} className='btn-no-hover ' icon={() => <span className='material-icons mt-[-3px]'>close</span>} />
      </View>
      <CloseAccountCreatorModal active={active} deactivate={deactivate} />

      <View className='mx-auto flex  flex-col sm:w-[536px]'>
        <Text className='font-rufina-stencil leading-40 text-[32px] font-normal text-[#1A1A1A] sm:text-center'>Take a selfie for verification</Text>
        <Text className='rubik mx-auto mt-[16px] text-base font-normal leading-6 text-[#323C58] sm:text-center lg:w-[488px]'>
          To confirm your identity, use your camera to capture a photograph of yourself. This image will not appear on your profile.
        </Text>
        <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A] '>Use your camera to take a picture of yourself.</Text>
        <View className='mt-[16px] flex flex-row'>
          <Button className='btn-no-hover_one mr-[12px] mt-[10px] flex h-8 w-8 items-center justify-center rounded-[100px] !bg-[#DFE2EA] sm:mt-[0px] sm:h-16 sm:w-16'>
            <span className='material-icons-outlined mt-[-4px] align-middle text-black'>person</span>
          </Button>
          <View className='w-full sm:w-auto'>
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined mr-[8px] mt-[-1px] text-[20px]'>photo_camera</span>}
              onClick={isCameraOpen ? closeCamera : openCamera}
              className='flex h-[60px] w-full rounded-[8px] !border-[#DFE2EA] !bg-[#FFFF] sm:w-[412px]'
            >
              <Text className='rubik text-[15px] font-medium leading-[20px]'> {isCameraOpen ? 'Close Camera' : 'Take a picture'}</Text>
            </Button>
            {isCameraOpen && (
              <Button
                variant='outline'
                icon={() => <span className='material-icons-outlined mr-[8px] mt-[-2px] text-[20px]'>photo</span>}
                onClick={takePicture}
                className='mt-[10px] flex h-[60px] rounded-[8px] !border-[#DFE2EA] !bg-[#FFFF] sm:w-[412px]'
              >
                <Text className='rubik text-[15px] font-medium leading-[20px]'>Take Picture</Text>
              </Button>
            )}
            <View className='flex flex-row items-center justify-between'>
              {isCameraOpen && (
                <div className='flex items-center justify-center'>
                  <video id='camera-feed' className='h-[230px] w-[230px]' autoPlay playsInline></video>
                </div>
              )}
              {capturedImageSelfie && (
                <div className='flex h-[230px] w-[230px] items-center justify-center'>
                  <img src={capturedImageSelfie} alt='Captured' />
                </div>
              )}
            </View>
          </View>
        </View>
        {!validSelfie && <Text className='rubik mx-auto mt-[16px] text-[15px] font-normal  leading-5 text-red-400'>Selfie picture is required.</Text>}
      </View>

      <View className='mt-[20px] flex  flex-col sm:mt-[123px] xl:w-[1320px]'>
        <div className='flex h-[6px] w-full'>
          <div className='h-full w-full bg-[#0B80E7]' />
        </div>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            icon={() => <span className='material-icons-outlined text-[19px] text-[#14171F]'>arrow_back_ios</span>}
            onClick={handleGoBack}
            className='bg-background-base flex h-[48px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#DFE2EA] !bg-[white]  px-4  py-2 sm:w-[103px]'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#14171F]'>Back</Text>
          </Button>

          <Button
            endIcon={() => !isSubmitting && <span className='material-icons-outlined text-[18px] text-[#FFF]'>arrow_forward_ios</span>}
            onClick={handleSubmit}
            disabled={isSubmitting}
            className='border-neutral bg-background-base flex h-[48px] items-center justify-center self-stretch self-stretch rounded-[8px] border !bg-[#0B80E7] sm:w-[135px]'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#FFF]'>
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </Text>
          </Button>
        </View>
        <div className='mt-[30px] flex items-center justify-center sm:mt-[0px]'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default OperatorFourthStepFlow;
