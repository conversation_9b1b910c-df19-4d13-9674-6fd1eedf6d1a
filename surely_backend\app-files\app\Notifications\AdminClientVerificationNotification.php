<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminClientVerificationNotification extends Notification
{
    use Queueable;

    private $userId;
    private $userName;
    private $companyName;

    /**
     * Create a new notification instance.
     *
     * @param int $userId
     * @param string $userName
     * @param string $companyName
     * @return void
     */
    public function __construct($userId, $userName, $companyName)
    {
        $this->userId = $userId;
        $this->userName = $userName;
        $this->companyName = $companyName;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $mailMessage = new MailMessage;
        $mailMessage->subject('New Client Verification - User ID: ' . $this->userId);
        $mailMessage->greeting('New Client Verification Submission');
        $mailMessage->line('A business client has submitted verification information:');
        $mailMessage->line('User ID: ' . $this->userId);
        $mailMessage->line('User Name: ' . $this->userName);
        $mailMessage->line('Company Name: ' . $this->companyName);
        
        $mailMessage->line('Please review this client verification in the admin panel.');
        
        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'user_id' => $this->userId,
            'user_name' => $this->userName,
            'company_name' => $this->companyName
        ];
    }
}
