import { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { Button, Checkbox, Divider, Popover, Dismissible, Image } from 'reshaped';
import useSurelyToast from '../../hooks/useSurelyToast';
import {
  format,
  addDays,
  isSameDay,
  isSameMonth,
  isSameYear,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  getHours,
  getMinutes,
  getDay,
  getMonth,
  setHours,
  setMinutes,
  parseISO,
  formatISO,
  parse,
  isBefore,
  isAfter,
  isPast,
  startOfDay,
} from 'date-fns';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

// Constants
const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
const today = new Date();
const ISO_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSxxx";
const formattedToday = format(today, ISO_FORMAT, { timeZone: 'local' });

// Utility functions
const isThisMonthsDay = (date) => isSameMonth(parseISO(date), parseISO(formattedToday));

const sameDayCheck = (date1, date2) =>
  isSameYear(parseISO(date1), parseISO(date2)) &&
  isSameMonth(parseISO(date1), parseISO(date2)) &&
  isSameDay(parseISO(date1), parseISO(date2));

const getCheckboxDate = (date) => {
  const inputDate = parseISO(date);
  return format(inputDate, 'd MMM yyyy', { timeZone: 'local' });
};

const formatTimeWithTimezone = (date) => format(date, ISO_FORMAT, { timeZone: 'local' });

// Input validation helpers
const validateHourInput = (input) => {
  input = input.replace(/\D/g, '');
  if (/^\d+$/.test(input)) {
    let validatedHour = +input;
    if (validatedHour > 23) {
      input = '23';
      validatedHour = 23;
    }
    return { input, validatedHour };
  }
  return { input, validatedHour: null };
};

const validateMinuteInput = (input) => {
  input = input.replace(/\D/g, '');
  if (/^\d+$/.test(input)) {
    let validatedMinute = +input;
    if (validatedMinute > 59) {
      input = '59';
      validatedMinute = 59;
    }
    return { input, validatedMinute };
  }
  return { input, validatedMinute: null };
};

const CalendarDay = ({
  active,
  date,
  handleDateSelect,
  handleRemoveDate,
  closeHandler,
  openHandler,
  isDateRangeSelected,
  toggleSwitch,
  firstOfRange,
  setFirstOfRange,
  lastOfRange,
  setLastOfRange,
  rangeShift,
  setRangeShift,
  fillRange,
  readOnly,
  isDatePopoverOpened,
  handleDateClick,
  closePopovers,
  calendarDays,
}) => {
  const toast = useSurelyToast();
  const dateObject = parseISO(date.day);
  const [isSelectedShift, setIsSelectedShift] = useState(date?.isSelected || null);
  const [isBeingSelectedShift, setIsBeingSelectedShift] = useState(date?.beingSelected || false);
  const [errorMessage, setErrorMessage] = useState(false);
  const [isInRange, setIsInRange] = useState(false);

  // Refs to track previous date and shift to prevent unnecessary updates
  const prevDateRef = useRef(date.day);
  const prevShiftRef = useRef(null);

  const [isOpenedPopover, setIsOpenedPopover] = useState(false);
  const [startHourMinute, setStartHourMinute] = useState({
    hour: getHours(parseISO(date.isSelected?.start ?? isSelectedShift?.start)) || 0,
    minute: getMinutes(parseISO(date.isSelected?.start ?? isSelectedShift?.start)) || 0,
  });

  const [endHourMinute, setEndHourMinute] = useState({
    hour: getHours(parseISO(date.isSelected?.end ?? isSelectedShift?.end)) || 0,
    minute: getMinutes(parseISO(date.isSelected?.end ?? isSelectedShift?.end)) || 0,
  });

  // Input value states for controlled inputs
  const startHourInitial = date.isSelected?.start || isSelectedShift?.start
    ? getHours(parseISO(date.isSelected?.start ?? isSelectedShift?.start)).toString() === '0'
      ? '00'
      : getHours(parseISO(date.isSelected?.start ?? isSelectedShift?.start)).toString()
    : '';

  const startMinuteInitial = date.isSelected?.start || isSelectedShift?.start
    ? getMinutes(parseISO(date.isSelected?.start ?? isSelectedShift?.start)).toString() === '0'
      ? '00'
      : getMinutes(parseISO(date.isSelected?.start ?? isSelectedShift?.start)).toString()
    : '';

  const endHourInitial = date.isSelected?.end || isSelectedShift?.end
    ? getHours(parseISO(date.isSelected?.end ?? isSelectedShift?.end)).toString() === '0'
      ? '00'
      : getHours(parseISO(date.isSelected?.end ?? isSelectedShift?.end)).toString()
    : '';

  const endMinuteInitial = date.isSelected?.end || isSelectedShift?.end
    ? getMinutes(parseISO(date.isSelected?.end ?? isSelectedShift?.end)).toString() === '0'
      ? '00'
      : getMinutes(parseISO(date.isSelected?.end ?? isSelectedShift?.end)).toString()
    : '';

  const [startHourValue, setStartHourValue] = useState(startHourInitial.padStart(2, '0'));
  const [startMinuteValue, setStartMinuteValue] = useState(startMinuteInitial.padStart(2, '0'));
  const [endHourValue, setEndHourValue] = useState(endHourInitial.padStart(2, '0'));
  const [endMinuteValue, setEndMinuteValue] = useState(endMinuteInitial.padStart(2, '0'));
  const [endDateValue, setEndDateValue] = useState(date.day);

  // isCheckboxChecked state removed

  // Helper to get initial minute value for dropdown, snapping to 5min intervals
  const getInitialMinuteForDropdown = (isoDateTimeString) => {
    if (!isoDateTimeString) return '00';
    const minutes = getMinutes(parseISO(isoDateTimeString));
    const roundedMinute = Math.round(minutes / 5) * 5;
    // Ensure it doesn't round up to 60, which should be 00 (handled by hour logic if it pushes to next hour)
    return (roundedMinute === 60 ? '55' : roundedMinute.toString().padStart(2, '0')); // if 60, use 55 to stay in current hour for simplicity
  };


  // Update input values when date or selected shift changes, but only on initial load or date change
  useEffect(() => {
    const newDay = date.day !== prevDateRef.current;
    // Check if the incoming prop 'date.isSelected' is different from the current internal numerical time state
    // This helps prevent resetting if the prop update is just confirming the user's latest change.
    let incomingTimesDifferFromInternal = false;
    if (date.isSelected) {
      const incomingStartHour = getHours(parseISO(date.isSelected.start));
      const incomingStartMinute = getMinutes(parseISO(date.isSelected.start));
      const incomingEndHour = getHours(parseISO(date.isSelected.end));
      const incomingEndMinute = getMinutes(parseISO(date.isSelected.end));

      if (incomingStartHour !== startHourMinute.hour ||
          incomingStartMinute !== startHourMinute.minute ||
          incomingEndHour !== endHourMinute.hour ||
          incomingEndMinute !== endHourMinute.minute) {
        incomingTimesDifferFromInternal = true;
      }
    } else if (isSelectedShift) { // If date.isSelected is null, but we have an internal shift, it means it was deselected.
        incomingTimesDifferFromInternal = true; // Treat deselection as a change to sync.
    }


    if (newDay || incomingTimesDifferFromInternal) {
      prevDateRef.current = date.day;

      const baseShift = date.isSelected || isSelectedShift; // Prioritize prop, fallback to state for initialization source

      const newStartHour = baseShift?.start
        ? getHours(parseISO(baseShift.start)).toString().padStart(2, '0')
        : '00';
      const newStartMinute = getInitialMinuteForDropdown(baseShift?.start);

      const newEndHour = baseShift?.end
        ? getHours(parseISO(baseShift.end)).toString().padStart(2, '0')
        : '00';
      const newEndMinute = getInitialMinuteForDropdown(baseShift?.end);

      setStartHourValue(newStartHour);
      setStartMinuteValue(newStartMinute);
      setEndHourValue(newEndHour);
      setEndMinuteValue(newEndMinute);

      // Only update numerical state if it's truly different to avoid re-triggering update effect unnecessarily
      if (startHourMinute.hour !== parseInt(newStartHour,10) || startHourMinute.minute !== parseInt(newStartMinute,10)) {
        setStartHourMinute({ hour: parseInt(newStartHour, 10), minute: parseInt(newStartMinute, 10) });
      }
      if (endHourMinute.hour !== parseInt(newEndHour,10) || endHourMinute.minute !== parseInt(newEndMinute,10)) {
        setEndHourMinute({ hour: parseInt(newEndHour, 10), minute: parseInt(newEndMinute, 10) });
      }

      // Update isSelectedShift state to be in sync with the prop if prop is source of truth
      if (date.isSelected) {
        setIsSelectedShift(date.isSelected); 
        prevShiftRef.current = date.isSelected.start + date.isSelected.end;
      } else {
        setIsSelectedShift(null);
        prevShiftRef.current = null;
      }
    }
  }, [date.day, date.isSelected]); // Depend on date.day and the prop date.isSelected

  useEffect(() => {
    if (
      firstOfRange &&
      lastOfRange &&
      isAfter(parseISO(date?.day), parseISO(firstOfRange)) &&
      isBefore(parseISO(date?.day), parseISO(lastOfRange)) &&
      !isBeingSelectedShift
    ) {
      setIsInRange(date.day);
    }

    return () => setIsInRange(false);
  }, [isBeingSelectedShift, firstOfRange, lastOfRange]);

  useEffect(() => {
    if (!isDateRangeSelected) {
      setFirstOfRange(null);
      setLastOfRange(null);
      if (lastOfRange === date.day) {
        togglePopover();
      }
      setIsBeingSelectedShift(false);
      if (
        firstOfRange &&
        lastOfRange &&
        isAfter(parseISO(date?.day), parseISO(firstOfRange)) &&
        isBefore(parseISO(date?.day), parseISO(lastOfRange)) &&
        !isBeingSelectedShift
      ) {
        setIsInRange(false);
      }
    }
  }, [isDateRangeSelected, isInRange]);

  useLayoutEffect(() => {
    const setInitialValues = () => {
      if (closePopovers) {
        setIsOpenedPopover(false);
        return;
      }
      if (isDatePopoverOpened !== date.day) {
        if (isDateRangeSelected && firstOfRange !== date.day && isDatePopoverOpened !== date.day) {
          setIsBeingSelectedShift(false);
        }
        setIsOpenedPopover(false);
        return;
      }

      // Handle checkbox state
      // Logic related to isCheckboxChecked previously here was removed.
      // isSelectedShift is the source of truth, managed by useEffect at line 165.
      // If date.isSelected is for another day, isSelectedShift should be cleared by that useEffect.
    };

    const timeoutId = setTimeout(setInitialValues, 0);

    return () => clearTimeout(timeoutId);
  }, [date, isSelectedShift, isBeingSelectedShift, isOpenedPopover]);

  const formatedStartTime = setMinutes(setHours(dateObject, startHourMinute.hour), startHourMinute.minute);
  // Use the selected end date for the end time
  const endDateObject = parseISO(endDateValue);
  const formatedEndTime = setMinutes(setHours(endDateObject, endHourMinute.hour), endHourMinute.minute);

  // Use a single shift time object with the ISO format
  const finalShiftTime = {
    start: formatTimeWithTimezone(formatedStartTime),
    end: formatTimeWithTimezone(formatedEndTime),
  };

  const togglePopover = () => {
    handleDateClick(date.day);
    if (isDateRangeSelected) {
      if (!firstOfRange && !lastOfRange) {
        setFirstOfRange(date.day);
        setIsBeingSelectedShift(date.day);
        setIsOpenedPopover(false);
        return;
      }
      if (firstOfRange && !lastOfRange && date.day > firstOfRange && firstOfRange !== date.day) {
        setLastOfRange(date.day);
        setIsBeingSelectedShift(date.day);
        setIsOpenedPopover((prevState) => !prevState);
        return;
      }
      if (firstOfRange && lastOfRange && lastOfRange === date.day) {
        setLastOfRange(null);
        setIsBeingSelectedShift(date.day);
        setIsOpenedPopover((prevState) => !prevState);
        return;
      }
      if (firstOfRange && lastOfRange && date.day > firstOfRange && lastOfRange !== date.day) {
        setLastOfRange(date.day);
        setIsBeingSelectedShift(date.day);
        setIsOpenedPopover((prevState) => !prevState);
        return;
      }
    } else {
      if (!isBefore(dateObject, startOfDay(new Date()))) {
        setIsOpenedPopover((prevState) => !prevState);
      } else if (isBefore(dateObject, startOfDay(new Date())) && isSelectedShift) {
        setIsOpenedPopover((prevState) => !prevState);
      }
    }
  };

  const validateShiftTimes = () => {
    const startDateTime = parseISO(finalShiftTime.start);
    const endDateTime = parseISO(finalShiftTime.end);
    const isCrossDayShift = !isSameDay(startDateTime, endDateTime);

    // Check if end time is before or equal to start time
    if (endDateTime <= startDateTime) {
      if (isCrossDayShift) {
        setErrorMessage('End time on the next day must be after start time.');
      } else {
        setErrorMessage('End time must be after start time.');
      }
      return false;
    }

    // Check if trying to select past times for today
    if (isSameDay(dateObject, new Date())) {
      const currentHour = new Date().getHours();
      const currentMinute = new Date().getMinutes();
      const selectedHour = getHours(parseISO(finalShiftTime.start));
      const selectedMinute = getMinutes(parseISO(finalShiftTime.start));

      if (selectedHour < currentHour || (selectedHour === currentHour && selectedMinute < currentMinute)) {
        setErrorMessage('Cannot select past times for today');
        return false;
      }
    }

    // If shift is longer than 24 hours, show a warning
    const shiftDurationHours = (endDateTime - startDateTime) / (1000 * 60 * 60);
    // For cross-day shifts, we need to ensure the end time is after the start time on the next day
    if (!isCrossDayShift && shiftDurationHours > 24) {
      setErrorMessage('Warning: Shift is longer than 24 hours. Please confirm this is correct.');
      // Still return true as this is just a warning
      return true;
    }

    setErrorMessage(false);
    return true;
  };

  const handleConfirmShiftAction = () => {
    if (isDateRangeSelected) {
      if (firstOfRange && lastOfRange && firstOfRange < date.day) {
        if (!validateShiftTimes()) return;
        setRangeShift(finalShiftTime);
        fillRange(firstOfRange, date, finalShiftTime); // 'date' here is the date object for the end of the range
        toggleSwitch(); // This likely closes the date range selection mode
        setIsBeingSelectedShift(false);
        setIsOpenedPopover(false);
        setFirstOfRange(null);
        setLastOfRange(null);
        return;
      }
      return; // Not a valid range confirmation scenario
    }

    // Logic for single date selection/update
    if (finalShiftTime.start && finalShiftTime.end) {
      if (!validateShiftTimes()) return;
      setIsSelectedShift(finalShiftTime);
      handleDateSelect(finalShiftTime); // Propagate to parent
      setIsOpenedPopover(false);
      return;
    }
  };

  const handleClearShiftAction = () => {
    // Pass the actual selected shift to handleRemoveDate if it exists
    if (isSelectedShift) {
      handleRemoveDate(isSelectedShift);
    } else {
      // Fallback, though less likely if button is disabled when !isSelectedShift
      handleRemoveDate(finalShiftTime);
    }
    
    setIsSelectedShift(null);
    setStartHourMinute({ hour: 0, minute: 0 });
    setEndHourMinute({ hour: 0, minute: 0 });
    // Reset input values to '00' for consistency with initial state if no shift
    setStartHourValue('00');
    setStartMinuteValue('00');
    setEndHourValue('00');
    setEndMinuteValue('00');
    setIsOpenedPopover(false);
  };


  // Auto-update useEffect (previously lines 434-449) removed as confirmation is now explicit via Button.

const handleStartHourChange = (e) => {
  const { value } = e.target;
  const { input, validatedHour } = validateHourInput(value);
  
  // Always update the input value for a smooth typing experience
  setStartHourValue(input);
  
  // Only validate when we have a complete hour (2 digits)
  if (input.length === 2 && validatedHour !== null) {
    // Only show past time validation for today's date
    if (isSameDay(dateObject, new Date())) {
      const currentHour = new Date().getHours();
      if (validatedHour < currentHour) {
        toast.showError('Cannot select past hours for today', 'Please select a future time for today\'s shifts.');
        // Reset to previous valid hour
        setStartHourValue(startHourMinute.hour.toString().padStart(2, '0'));
        return;
      }
    }
    
    // Only update the hourMinute state when we have a complete, valid hour
    setStartHourMinute((prev) => ({ ...prev, hour: validatedHour }));

    if (validatedHour >= 20) {
      const nextDayISO = formatTimeWithTimezone(addDays(dateObject, 1));
      // Only update if endDateValue is not already set to this nextDayISO, to avoid redundant state updates.
      // Comparing full ISO strings might be too strict if only date matters.
      // Let's compare the calendar day part.
      const endDateValueCalDay = format(parseISO(endDateValue), 'yyyy-MM-dd');
      const nextDayCalDay = format(parseISO(nextDayISO), 'yyyy-MM-dd');
      if (endDateValueCalDay !== nextDayCalDay) {
          setEndDateValue(nextDayISO);
      }
    } else {
      // If start hour is before 8 PM, reset endDateValue to current day if it was advanced.
      const currentPopoverDayISO = date.day; // This is the ISO string for the day this popover instance represents
      
      // Compare calendar day part of endDateValue with calendar day part of currentPopoverDayISO
      const endDateValueCalDay = format(parseISO(endDateValue), 'yyyy-MM-dd');
      const currentPopoverCalDay = format(parseISO(currentPopoverDayISO), 'yyyy-MM-dd');

      if (endDateValueCalDay !== currentPopoverCalDay) {
        setEndDateValue(currentPopoverDayISO); // Reset to the current popover's day
      }
    }
  } else {
    // If the input is not valid (e.g., empty or non-numeric after replace), just update the display value
    setStartHourValue(input);
  }
};

  const handleStartMinuteChange = (e) => {
    const { value } = e.target;
    const { input, validatedMinute } = validateMinuteInput(value);

    // If the input is valid and we have a validated minute
    if (validatedMinute !== null) {
      // Check if it's today and current hour
      if (isSameDay(dateObject, new Date())) {
        const currentHour = new Date().getHours();
        const currentMinute = new Date().getMinutes();
        if (startHourMinute.hour === currentHour && validatedMinute < currentMinute) {
          toast.showError('Cannot select past minutes for current hour', 'Please select a future time for today\'s shifts.');
          setStartMinuteValue(startHourMinute.minute.toString().padStart(2, '0'));
          return;
        }
      }

      setStartMinuteValue(input);
      setStartHourMinute((prev) => ({ ...prev, minute: validatedMinute }));
    } else {
      // If the input is not valid, just update the display value
      setStartMinuteValue(input);
    }
  };

  const handleEndHourChange = (e) => {
    const { value } = e.target;
    const { input, validatedHour } = validateHourInput(value);

    // If the input is valid and we have a validated hour
    if (validatedHour !== null) {
      setEndHourValue(input);
      setEndHourMinute((prev) => ({ ...prev, hour: validatedHour }));
    } else {
      // If the input is not valid, just update the display value
      setEndHourValue(input);
    }
  };

  const handleEndMinuteChange = (e) => {
    const { value } = e.target;
    const { input, validatedMinute } = validateMinuteInput(value);

    // If the input is valid and we have a validated minute
    if (validatedMinute !== null) {
      setEndMinuteValue(input);
      setEndHourMinute((prev) => ({ ...prev, minute: validatedMinute }));
    } else {
      // If the input is not valid, just update the display value
      setEndMinuteValue(input);
    }
  };

  const handleEndDateChange = (e) => {
    setEndDateValue(e.target.value);
  };


  const isSelected = (date?.isSelected ?? isBeingSelectedShift === date.day) || isInRange;
  const isPastDate = isPast(parseISO(date.day));

  const selectedStyles = '!text-white !bg-[#0B80E7] !border-[#0B80E7]';
  const defaultStyles = '!border-[#D5D4DF]';
  const pastStyles = '!bg-[#F2F3F7] !border-[#D5D4DF] !text-[#D5D4DF]';
  const pastSelectedStyles = selectedStyles + pastStyles;
  const crossDayStartStyles = 'relative after:content-[""] after:absolute after:bottom-1 after:right-1 after:w-3 after:h-3 after:bg-[#F4BF00] after:rounded-full';
  const crossDayEndStyles = 'relative after:content-[""] after:absolute after:bottom-1 after:left-1 after:w-3 after:h-3 after:bg-[#F4BF00] after:rounded-full';

  const buttonStyles = `
    ${isSelected ? selectedStyles : defaultStyles}
    ${isBefore(dateObject, startOfDay(new Date())) && !isSelected ? pastStyles : ''}
    ${date.isCrossDayShiftStart ? crossDayStartStyles : ''}
    ${date.isCrossDayShiftEnd ? crossDayEndStyles : ''}
  `;

  const singleDay = date?.day?.split('T')[0].slice(-2)[0] === '0' ? date?.day?.split('T')[0].slice(-1) : date?.day?.split('T')[0].slice(-2);

  const checkboxDate = getCheckboxDate(date.day);

  const calculatePopoverPosition = (dateString) => {
    const POPOVER_HEIGHT = 350;
    const index = calendarDays?.findIndex((d) => isSameDay(parseISO(d), parseISO(dateString)));
    const row = Math.floor(index / 7);
    const totalRows = Math.ceil(calendarDays.length / 7);

    const viewportHeight = window.innerHeight;

    const elementPosition = row * 60;

    if (elementPosition + POPOVER_HEIGHT > viewportHeight || row >= totalRows - 2) {
      return 'top-start';
    }
    return 'bottom-start';
  };


  return (
    <Popover active={isOpenedPopover} position={calculatePopoverPosition(date.day)} className='calendar-popover'>
      <Popover.Trigger>
        {(attributes) => {
          return (
            <Button
              attributes={attributes}
              onClick={togglePopover}
              className={`flex h-6 w-full max-w-[108px] cursor-pointer items-center justify-center rounded-md !border lg:h-[60px] ${buttonStyles}`}
              variant='outline'
            >
              {singleDay}
            </Button>
          );
        }}
      </Popover.Trigger>
      <Popover.Content
        className='flex w-[244px] flex-col border-none !bg-[#323C58] p-4'
        style={{
          maxHeight: '100vh',
          transform: 'none',
        }}
      >
        <div className='rubik mb-2 w-full rounded-lg bg-[#F4F5F7] px-4 py-[14px]'>
          <p className='mb-3 font-medium text-[#1A1A1A]'>Shift start:</p>
          <div className='flex flex-col gap-3'>
            <div className='flex items-center'>
              <p className='w-20 font-medium text-[#1A1A1A]'>Date:</p>
              <p className='font-medium text-[#1A1A1A]'>{format(dateObject, 'dd MMM yyyy')}</p>
            </div>
            <div className='flex items-center justify-evenly gap-2'>
              <p className='w-12 font-medium text-[#1A1A1A]'>Time:</p>
              <input
                type="text"
                disabled={readOnly}
                name='shiftStartHour'
                value={startHourValue}
                onChange={handleStartHourChange}
                placeholder="00"
                maxLength="2"
                className='h-[48px] w-14 rounded border border-[#BBC1D3] bg-white px-3 py-[14px] leading-5 text-center'
                style={{ fontFamily: 'Rubik' }}
              />
              <span className='text-2xl'>:</span>
              <input
                type="text"
                disabled={readOnly}
                name='shiftStartMinute'
                value={startMinuteValue}
                onChange={handleStartMinuteChange}
                placeholder="00"
                maxLength="2"
                className='h-[48px] w-14 rounded border border-[#BBC1D3] bg-white px-3 py-[14px] leading-5 text-center'
                style={{ fontFamily: 'Rubik' }}
              />
            </div>
          </div>
        </div>
        <div className='rubik w-full rounded-lg bg-[#F4F5F7] px-4 py-[14px]'>
          <p className='mb-3 font-medium text-[#1A1A1A]'>Shift end:</p>
          <div className='flex flex-col gap-3'>
            <div className='flex items-center'>
              <p className='w-20 font-medium text-[#1A1A1A]'>Date:</p>
              <select
                disabled={readOnly}
                name='endDateSelect'
                value={endDateValue}
                onChange={handleEndDateChange}
                className='h-[36px] rounded border border-[#BBC1D3] bg-white px-3 py-[8px] leading-5 text-center appearance-none'
                style={{ fontFamily: 'Rubik' }}
              >
                <option value={date.day}>{format(dateObject, 'dd MMM yyyy')}</option>
                <option value={formatTimeWithTimezone(addDays(dateObject, 1))}>{format(addDays(dateObject, 1), 'dd MMM yyyy')}</option>
              </select>
            </div>
            <div className='flex items-center justify-evenly gap-2'>
              <p className='w-12 font-medium text-[#1A1A1A]'>Time:</p>
              <input
                type="text"
                disabled={readOnly}
                name='shiftEndHour'
                value={endHourValue}
                onChange={handleEndHourChange}
                placeholder="00"
                maxLength="2"
                className='h-[48px] w-14 rounded border border-[#BBC1D3] bg-white px-3 py-[14px] leading-5 text-center'
                style={{ fontFamily: 'Rubik' }}
              />
              <span className='text-2xl'>:</span>
              <input
                type="text"
                disabled={readOnly}
                name='shiftEndMinute'
                value={endMinuteValue}
                onChange={handleEndMinuteChange}
                placeholder="00"
                maxLength="2"
                className='h-[48px] w-14 rounded border border-[#BBC1D3] bg-white px-3 py-[14px] leading-5 text-center'
                style={{ fontFamily: 'Rubik' }}
              />
            </div>
          </div>
        </div>
        <Divider className='my-3' />
        {errorMessage && (
          <>
            <div className='rubik text-[#CB101D]'>{errorMessage}</div>
            <Divider className='my-3' />
          </>
        )}
        <div className='flex items-center justify-end gap-3 mt-3'>
          <Button
            variant="outline"
            color="critical"
            onClick={handleClearShiftAction}
            disabled={readOnly || !isSelectedShift}
            attributes={{'aria-label': `Clear shift for ${checkboxDate}`}}
          >
            Clear
          </Button>
          <Button
            onClick={handleConfirmShiftAction}
            disabled={readOnly || isBefore(dateObject, startOfDay(new Date())) || !!errorMessage}
            attributes={{'aria-label': `Confirm shift for ${checkboxDate}`}}
          >
            Confirm
          </Button>
        </div>
      </Popover.Content>
    </Popover>
  );
};

const CalendarDayGrid = ({
  calendarDays,
  selectedDates,
  closePopovers,
  rangeGroup,
  currentMonthStart,
  handleSelectDates,
  handleRemoveDates,
  readOnly,
}) => {
  const [selectedDatesChild, setSelectedDatesChild] = useState([]);
  const [firstOfRange, setFirstOfRange] = useState(null);
  const [lastOfRange, setLastOfRange] = useState(null);
  const [rangeTotal, setRangeTotal] = useState([]);
  const [rangeShift, setRangeShift] = useState();
  const [isDatePopoverOpened, setIsDatePopoverOpened] = useState();

  const handleDateClick = (date) => {
    setIsDatePopoverOpened(date);
  };

  const { isDateRangeSelected, toggleSwitch, rangeStart, rangeEnd } = rangeGroup;

  const previousDates = selectedDates;

  const handleNewSelectedDate = (date) => {
    if (date?.length) {
      const unsubmittedTotalSelectedDates = [...selectedDatesChild, ...date];
      setSelectedDatesChild((prevState) => [...prevState]);
      handleSelectDates(date);
    }
    if (date?.start) {
      const unsubmittedTotalSelectedDates = [...selectedDatesChild, date];
      setSelectedDatesChild((prevState) => [...prevState]);
      handleSelectDates(date);
    }
  };

  const handleDateSubmit = (dates) => {
    handleSelectDates(dates);
  };

  const handleRemoveDate = (date) => {
    handleRemoveDates(date);
  };

  const fillRange = (rangeStart, rangeEnd, rangeShift) => {
    const startDate = parseISO(rangeStart);
    const endDate = parseISO(rangeEnd.day);

    const range = calendarDays
      ?.map((date) => {
        const currentDate = parseISO(date);
        if (currentDate >= startDate && currentDate <= endDate) {
          const startHourMinute = {
            hour: getHours(parseISO(rangeShift?.start)) || 0,
            minute: getMinutes(parseISO(rangeShift?.start)) || 0,
          };

          const endHourMinute = {
            hour: getHours(parseISO(rangeShift?.end)) || 0,
            minute: getMinutes(parseISO(rangeShift?.end)) || 0,
          };

          const isSelected = true;
          const formatedStartTime = setMinutes(setHours(currentDate, startHourMinute?.hour ?? 0), startHourMinute?.minute ?? 0);
          const formatedEndTime = setMinutes(setHours(currentDate, endHourMinute?.hour ?? 0), endHourMinute?.minute ?? 0);

          const formattedDate = {
            start: formatTimeWithTimezone(formatedStartTime),
            end: formatTimeWithTimezone(formatedEndTime),
          };

          return formattedDate;
        }
        return null;
      })
      .filter(Boolean);

    handleNewSelectedDate(range);
    setRangeTotal(range);
    setFirstOfRange(null);
    setLastOfRange(null);
  };

  return (
    <div className='grid grid-cols-7 justify-center gap-4'>
      {weekDays.map((day) => (
        <div className='text-center font-medium' key={day}>
          {day}
        </div>
      ))}
      {calendarDays?.map((date, index) => {
        // Find if this date is selected
        const isSelected =
          selectedDates.find((selDate) => {
            if (format(parseISO(selDate.start), 'P') === format(parseISO(date), 'P')) {
              return { start: parseISO(selDate.start), end: parseISO(selDate.end) };
            } else return null;
          }) ||
          rangeTotal?.find((rangeDate) => {
            if (format(parseISO(rangeDate.start), 'P') === format(parseISO(date), 'P')) {
              return { start: parseISO(rangeDate?.start), end: parseISO(rangeDate?.end) };
            } else return null;
          }) ||
          null;

        // Check if this date is the start of a cross-day shift
        const isCrossDayShiftStart = isSelected && 
          !isSameDay(parseISO(isSelected.start), parseISO(isSelected.end));

        // Check if this date is the end of a cross-day shift from a previous day
        const isCrossDayShiftEnd = selectedDates.some(selDate => 
          format(parseISO(selDate.end), 'P') === format(parseISO(date), 'P') && 
          !isSameDay(parseISO(selDate.start), parseISO(selDate.end)) &&
          format(parseISO(selDate.start), 'P') !== format(parseISO(date), 'P')
        );

        const formattedDate = { 
          day: date, 
          isSelected, 
          isCrossDayShiftStart,
          isCrossDayShiftEnd
        };

        return (
          <div key={date.day + ' - ' + index}>
            <CalendarDay
              active={true}
              date={formattedDate}
              handleDateSelect={handleNewSelectedDate}
              handleRemoveDate={handleRemoveDate}
              closeHandler={() => {}}
              openHandler={() => {}}
              isDateRangeSelected={isDateRangeSelected}
              toggleSwitch={toggleSwitch}
              firstOfRange={firstOfRange}
              setFirstOfRange={setFirstOfRange}
              lastOfRange={lastOfRange}
              setLastOfRange={setLastOfRange}
              rangeShift={rangeShift}
              setRangeShift={setRangeShift}
              fillRange={fillRange}
              readOnly={readOnly}
              isDatePopoverOpened={isDatePopoverOpened}
              handleDateClick={handleDateClick}
              closePopovers={closePopovers}
              calendarDays={calendarDays}
            />
          </div>
        );
      })}
    </div>
  );
};

export default CalendarDayGrid;
