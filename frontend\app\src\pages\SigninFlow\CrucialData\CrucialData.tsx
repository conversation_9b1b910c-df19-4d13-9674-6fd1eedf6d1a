// @ts-nocheck
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button, Text, View, TextField, Image, useToggle } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import { useRegistrationContext } from 'src/context/RegistrationContext';
import { registerOperator } from 'src/services/user';
import CloseAccountCreatorModal from '../CloseAccountCreatorModal/CloseAccountCreatorModal';
import { addGeneral, getGeneral, getCities } from 'src/services/settings';
import { AuthContext } from 'src/context/AuthContext';
import SurelyBottom from '../SurelyBottom/SurelyBottom';
import { AppContext } from 'src/context/AppContext';
import { useModalState } from 'src/context/ModalContext';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const CrucialData: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { sharedRegisterData, setSharedRegisterData } = useRegistrationContext();
  const { authenticateUser } = useContext(AuthContext);
  const { fetchAppData } = useContext(AppContext);
  const { active, activate, deactivate } = useToggle(false);
  const [selectedType, setSelectedType] = useState(location.state?.userType || null);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [phoneNumberPrefix, setPhoneNumberPrefix] = useState<any>('44');
  const [phoneNumberValid, setPhoneNumberValid] = useState(true);
  const [postCode, setPostCode] = useState<string>('');
  const [postcodeValid, setPostcodeValid] = useState(true);
  const [city, setCity] = useState();
  const [adminDistrict, setAdminDistrict] = useState();
  const [region, setRegion] = useState();
  const [country, setCountry] = useState();
  const [searchParams1, setSearchParams] = useSearchParams();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [token, setToken] = useState(location.state?.referralToken ?? null);

  const toastSystem = useToastSystem();

  useEffect(() => {
    if (localStorage.getItem('isLinkedInReady') === 'true') {
      localStorage.removeItem('isLinkedInReady');
      return;
    }
    const searchParams = new URLSearchParams(window.location.href);
    const linkedInCode = String(searchParams1.get('code') ?? '');
    const accessToken = searchParams.get('access_token');

    localStorage.setItem('google_token', accessToken ?? '');

    if (linkedInCode) {
      localStorage.setItem('linkedIn_code', linkedInCode);
      localStorage.setItem('isLinkedInReady', 'true');
      close();
    }
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  const handleCreateAccount = async () => {
    if (!selectedType) {
      toastSystem.showError('Choose one of these options:', 'Are you a Security operative or a Client?');
      return;
    }
    if (!phoneNumberPrefix || !phoneNumber) {
      toastSystem.showError('Enter Phone Number:', 'This field is required.');
      setPhoneNumberValid(false);
      return;
    }
    if (!postCode) {
      toastSystem.showError('Enter your post code:', 'This field is required.');
      setPostcodeValid(false);
      return;
    }

    setPostcodeValid(postCode.trim() !== '');
    const account_type = selectedType === 'client' ? 2 : 1;
    const phone = phoneNumberPrefix + phoneNumber;

    const cityInfo = `${adminDistrict}, ${region}, ${country}`;

    setSharedRegisterData((sharedRegisterData: any) => ({
      ...sharedRegisterData,
      crucialData: {
        ...sharedRegisterData.crucialData,
        accountType: account_type,
        phone,
        city: cityInfo,
        postCode,
        linkedInToken: localStorage.getItem('linkedIn_code'),
        ref: 'register',
        referFriend: token && token.trim() ? token : null,
      },
    }));

    const operatorRegisterFinalData = {
      ...sharedRegisterData.baseData,
      accountType: account_type,
      phone,
      city: cityInfo,
      postCode,
      ref: 'register',
      referFriend: token && token.trim() ? token : null,
    };

    try {
      let response = await registerOperator(operatorRegisterFinalData);
      if (response.token) {
        authenticateUser(response);
      }
      if (selectedType === 'client') {
        fetchAppData();
        navigate('/search-operator'); // Send client directly to search operator page after sign up
        // navigate('/verify-email');
      } else {
        fetchAppData();
        navigate('/my-profile');
        // navigate('/verify-email');
      }
      toastSystem.showSuccess('Account Created Successfully', 'Welcome to Surely! Get ready to explore and make the most of Surely. Thank you for joining us!');
    } catch (error) {
      toastSystem.showError('Error', error?.message);
    }
  };

  useEffect(() => {
    if (postCode) {
      const formattedPostCode = postCode.replace(/\s/g, '');
      getCities(formattedPostCode).then((data: any) => {
        setAdminDistrict(data.admin_district);
        setRegion(data.region);
        setCountry(data.country);
      });
    }
  }, [postCode]);

  return (
    <View className='mt-[20px] flex flex-col sm:mt-[84.3px]'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button variant='ghost' onClick={activate} icon={() => <span className='material-icons mt-[-3px]'>close</span>} />
      </View>
      <View className='mx-auto flex w-full flex-col px-[12px] sm:w-[488px] lg:px-0'>
        <Text className='font-rufina-stencil text-center text-[32px] font-normal leading-[40px] text-[#1A1A1A]'>Let’s add some details</Text>
        <Text className=' rubik mt-[16px] text-center text-[16px] font-normal leading-[24px] text-[#323C58]'>
          In order to assist you with the setup process, we require essential information from you.
        </Text>
        <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>I’m signing up as</Text>
        <View className='mx-auto mt-[10px] flex w-full flex-col justify-between gap-4 sm:flex-row  lg:gap-0'>
          <View className='flex w-full flex-col lg:max-w-[488px]'>
            <Button
              variant='outline'
              icon={() => (
                <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none'>
                  <path
                    d='M8.0001 0.951233C8.94825 0.951233 9.85756 1.32788 10.528 1.99833C11.1984 2.66877 11.5751 3.57808 11.5751 4.52623C11.5751 5.47438 11.1984 6.3837 10.528 7.05414C9.85756 7.72458 8.94825 8.10123 8.0001 8.10123C7.05195 8.10123 6.14263 7.72458 5.47219 7.05414C4.80175 6.3837 4.4251 5.47438 4.4251 4.52623C4.4251 3.57808 4.80175 2.66877 5.47219 1.99833C6.14263 1.32788 7.05195 0.951233 8.0001 0.951233ZM8.0001 9.88873C11.9505 9.88873 15.1501 11.4885 15.1501 13.4637V15.2512H0.850098V13.4637C0.850098 11.4885 4.04972 9.88873 8.0001 9.88873Z'
                    fill='#323C58'
                  />
                </svg>
              )}
              onClick={() => setSelectedType('client')}
              className={`rubik flex items-center justify-center gap-2 self-stretch border p-4 ${
                selectedType === 'client' ? '!bg-[#E8F4FF]' : '!bg-[#ffff]'
              } h-[56px] w-auto rounded-md lg:w-[238px]`}
            >
              <Text className='rubik text-[16px] font-medium leading-[24px] text-[#323C58]'>Client</Text>
            </Button>
            <Text className=' rubik mt-[8px] text-center text-[14px] font-normal leading-[20px] text-[#3C455D]'>I want to hire</Text>
          </View>
          <View className='flex w-full  flex-col sm:mt-[0px] lg:w-auto'>
            <Button
              variant='outline'
              icon={() => (
                <svg xmlns='http://www.w3.org/2000/svg' width='12' height='16' viewBox='0 0 12 16' fill='none'>
                  <path
                    d='M5.99994 0.831848L0.0522461 3.47527V7.4404C0.0522461 11.1081 2.58993 14.538 5.99994 15.3707C9.40996 14.538 11.9476 11.1081 11.9476 7.4404V3.47527L5.99994 0.831848ZM5.99994 8.09465H10.6259C10.2757 10.8174 8.45833 13.2427 5.99994 14.0027V8.10126H1.37396V4.33438L5.99994 2.27912V8.09465Z'
                    fill='#323C58'
                  />
                </svg>
              )}
              onClick={() => setSelectedType('operative')}
              className={`rubik flex items-center justify-center gap-2 self-stretch border p-4 ${
                selectedType === 'operative' ? '!bg-[#E8F4FF]' : '!bg-[#ffff]'
              } h-[56px] w-full rounded-md lg:w-[238px]`}
            >
              <Text className='rubik text-[16px] font-medium leading-[24px] text-[#323C58]'>Security Operative</Text>
            </Button>
            <Text className='rubik mt-[8px] text-center text-[14px] font-normal leading-[20px] text-[#3C455D]'>
              I’m looking for job opportunities
            </Text>
          </View>
        </View>
        <Text className='rubik mt-[16px] mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>Your mobile phone</Text>
        <View className='mt-[16px] flex flex-row'>
          <View>
            <PhoneInput
              country={'gb'}
              value={phoneNumberPrefix}
              onChange={(phone) => setPhoneNumberPrefix(phone)}
              inputStyle={{
                width: `${isSmallScreen ? '100%' : '122px'}`,
                height: '48px',
                color: '#1A1A1A',
              }}
            />
          </View>

          <TextField
            name='number'
            placeholder='Type in mobile phone'
            value={phoneNumber}
            onChange={(e) => {
              const value = e.value;
              const numbersOnly = value.replace(/[^0-9]/g, '');
              setPhoneNumber(numbersOnly);
              setPhoneNumberValid(numbersOnly.length > 0);
            }}
            className='ml-2 h-[48px] w-full'
          />
        </View>
        {!phoneNumberValid && <Text className='rubik mt-[16px] text-[15px] font-normal leading-[20px] text-red-400'>Phone number is required.</Text>}

        <Text className='rubik mt-[16px] text-[15px] font-normal leading-[20px]  text-[#444B5F]'>
          We will verify your number by sending a text message containing a four-digit code.
        </Text>
        <View className='mt-[16px] grid grid-cols-2 items-center justify-between gap-3 sm:flex sm:flex-row'>
          <View className='flex flex-col'>
            <Text className='rubik text-[15px] font-medium leading-[20px] text-[#1A1A1A]'>Your postcode</Text>
            <TextField
              name='postcode'
              className=' mt-[5px] px-[12px] py-[14px] sm:w-[108px]'
              placeholder='E1 6AN'
              value={postCode}
              onChange={(e) => setPostCode(e.value)}
            />
          </View>
          <View className='flex flex-col'>
            <Text className='rubik text-[15px] font-medium leading-[20px] text-[#1A1A1A] '>District</Text>

            <View className='mt-[5px] flex h-[48px] items-center gap-[8px] self-stretch overflow-auto rounded-[4px] border border-[1px] border-[#BBC1D3] bg-[#fff]  sm:w-[360px] '>
              {adminDistrict || region || country ? (
                <>
                  <Text className='rubik mx-[10px] text-[14px] font-normal leading-[20px] text-[#3C455D] lg:mt-0 '>
                    {adminDistrict}, {region}, {country}
                  </Text>
                </>
              ) : (
                <Text className='rubik px-3 text-[14px] font-normal leading-[20px] text-[#3C455D]'>Your postal town or city</Text>
              )}
            </View>
          </View>
        </View>
        {!postcodeValid && <Text className='rubik mt-[16px] text-[15px] font-normal leading-[20px]  text-red-400'>Postcode is required.</Text>}
      </View>

      <View className='mt-[30px] flex  flex-col px-[12px] sm:mt-[123px] lg:px-0 xl:w-[1320px]'>
        <div className='flex h-[6px] w-full'>
          <div className='h-full w-1/2 rounded-bl-[8px] rounded-tl-[8px] bg-[#0B80E7]' />
          <div className='h-full w-1/2 rounded-br-[8px] rounded-tr-[8px] bg-[#F4F5F7]' />
        </div>
        <View className='mt-[70px] flex flex-row justify-between gap-2'>
          <Button
            variant='outline'
            icon={() => (
              <svg xmlns='http://www.w3.org/2000/svg' width='14' height='15' viewBox='0 0 14 15' fill='none'>
                <path
                  id='close'
                  d='M1.4313 0.937714L0.351562 2.01746L5.92029 7.58618L0.351562 13.1549L1.4313 14.2346L7.00003 8.66592L12.5688 14.2346L13.6485 13.1549L8.07977 7.58618L13.6485 2.01746L12.5688 0.937714L7.00003 6.50644L1.4313 0.937714Z'
                  fill='black'
                />
              </svg>
            )}
            onClick={activate}
            className='border-neutral bg-background-base flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border  !bg-[white] px-4 py-2 sm:w-[180px]'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] !text-[#000000]'>Close</Text>
          </Button>

          <CloseAccountCreatorModal active={active} deactivate={deactivate} />

          <Button
            endIcon={() => (
              <svg xmlns='http://www.w3.org/2000/svg' width='8' height='15' viewBox='0 0 8 15' fill='none'>
                <path
                  id='short-arrow-right'
                  d='M0.0898438 13.0341L1.27591 14.2201L7.90984 7.58619L1.27591 0.952255L0.0898438 2.13832L5.53771 7.58619L0.0898438 13.0341Z'
                  fill='white'
                />
              </svg>
            )}
            onClick={handleCreateAccount}
            className='border-neutral bg-background-base flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border  !bg-[#0B80E7] px-4 py-2 sm:w-[180px] '
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] !text-white'>Create account</Text>
          </Button>
        </View>
        <div className='mt-[55px] flex items-center justify-center'>
          <SurelyBottom />
        </div>
      </View>
    </View>
  );
};

export default CrucialData;
