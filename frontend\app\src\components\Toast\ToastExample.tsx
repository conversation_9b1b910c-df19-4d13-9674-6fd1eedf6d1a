// @ts-nocheck
import React from 'react';
import { View, Button } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import useSurelyToast from '../../hooks/useSurelyToast';

/**
 * Example component demonstrating how to use the SurelyToast system
 * This component can be used as a reference for implementing toasts throughout the app
 */
const ToastExample: React.FC = () => {
  const navigate = useNavigate();
  const { 
    show, 
    showSuccess, 
    showError, 
    showInfo, 
    showWelcome, 
    hideAll, 
    renderToasts 
  } = useSurelyToast();

  const handleShowWelcomeToast = () => {
    showWelcome(
      'Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile.',
      {
        text: 'Complete your profile',
        onClick: () => {
          console.log('Navigate to profile completion');
          // navigate('/operator-settings');
        }
      }
    );
  };

  const handleShowPaymentToast = () => {
    showWelcome(
      'In order to start hiring, you will need to fill in your card details in the payment settings section.',
      {
        text: 'Proceed to settings',
        onClick: () => {
          console.log('Navigate to payment settings');
          // navigate('/client-settings-payment', { state: { activeTab: '2' } });
        }
      }
    );
  };

  const handleShowSuccessToast = () => {
    showSuccess(
      'Congratulations!',
      'Your Job Post is Now Live'
    );
  };

  const handleShowErrorToast = () => {
    showError(
      'Error',
      'An error occurred while processing your request. Please try again.'
    );
  };

  const handleShowInfoToast = () => {
    showInfo(
      'New message',
      'You have received a new message from John Doe.'
    );
  };

  const handleShowCustomToast = () => {
    show({
      title: 'Custom Toast',
      content: 'This is a custom toast without an icon and with a longer timeout.',
      showIcon: false,
      timeout: 10000,
      button: {
        text: 'Custom Action',
        onClick: () => console.log('Custom action clicked')
      }
    });
  };

  return (
    <View className="p-6 space-y-4">
      <h2 className="text-xl font-bold mb-4">Toast Examples</h2>
      
      <View className="space-y-2">
        <Button onClick={handleShowWelcomeToast}>
          Show Welcome Toast (Profile)
        </Button>
        
        <Button onClick={handleShowPaymentToast}>
          Show Welcome Toast (Payment)
        </Button>
        
        <Button onClick={handleShowSuccessToast}>
          Show Success Toast
        </Button>
        
        <Button onClick={handleShowErrorToast}>
          Show Error Toast
        </Button>
        
        <Button onClick={handleShowInfoToast}>
          Show Info Toast
        </Button>
        
        <Button onClick={handleShowCustomToast}>
          Show Custom Toast
        </Button>
        
        <Button onClick={hideAll} variant="outline">
          Hide All Toasts
        </Button>
      </View>

      {/* Render all active toasts */}
      {renderToasts()}
    </View>
  );
};

export default ToastExample;
