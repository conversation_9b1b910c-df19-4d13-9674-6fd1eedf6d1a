@extends('layouts.app')

@section('content')
<div class="layout-px-spacing">
    <div class="row layout-top-spacing">
        <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
            <div class="widget-content widget-content-area br-6">
                <div class="invoice-container">
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="invoice-content">
                                <div class="invoice-detail-header">
                                    <div class="row justify-content-between">
                                        <div class="col-xl-5 invoice-address-company">
                                            <h4>Invoice</h4>
                                            <div class="invoice-address-company-fields">
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Invoice ID</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">{{ $invoice->id }}</p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Contract ID</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">{{ $contract->id }}</p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Type</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><span class="badge {{ $invoice->type == 'escrow' ? 'badge-info' : 'badge-primary' }}">{{ ucfirst($invoice->type) }}</span></p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Status</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">
                                                            <span class="badge {{ $invoice->status == 'pending' ? 'badge-warning' : 'badge-success' }}">{{ ucfirst($invoice->status) }}</span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Payment Status</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">
                                                            <span class="badge {{ $payment_status == 'paid' ? 'badge-success' : 'badge-warning' }}">{{ ucfirst($payment_status) }}</span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Issue Date</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">{{ $issueDate }}</p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Due Date</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">{{ $dueDate }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5 invoice-address-client">
                                            <h4>Bill To:</h4>
                                            <div class="invoice-address-client-fields">
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Name</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">{{ $contract->client->name }}</p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Email</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">{{ $contract->client->email }}</p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Phone</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">{{ $contract->client->phone ?? 'N/A' }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="invoice-detail-items">
                                    <div class="table-responsive">
                                        <table class="table table-bordered item-table">
                                            <thead>
                                                <tr>
                                                    <th>Description</th>
                                                    <th>Type</th>
                                                    <th>Sub Amount</th>
                                                    <th>Surely fees (Fee + VAT)</th>
                                                    @if($contract->job && $contract->job->is_emergency_hire)
                                                        <th>Emergency Hire Fee ({{$contract->emergency_hire_fee_rate}}%)</th>
                                                    @endif
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>{{ $invoice->description ?: ($invoice->type == 'escrow' ? 'Escrow Payment' : 'Outstanding Payment') }}</td>
                                                    <td>
                                                        <span class="badge {{ $invoice->type == 'escrow' ? 'badge-info' : 'badge-primary' }}">{{ ucfirst($invoice->type) }}</span>
                                                    </td>
                                                    <td>{{ $invoice->currency }} {{ number_format($invoice->sub_amount, 2) }}</td>
                                                    <td>{{ $invoice->currency }} {{ number_format($invoice->amount - $invoice->sub_amount, 2) }}</td>
                                                    @if($contract->job && $contract->job->is_emergency_hire)
                                                        <td>{{ $invoice->currency }} {{ $invoice->type == 'escrow' ? number_format($contract->emergency_hire_fee_amount, 2) : '0.00' }}</td>
                                                    @endif
                                                    <td>{{ $invoice->currency }} {{ number_format($invoice->amount, 2) }}</td>
                                                </tr>
                                                @if($invoice->type == 'escrow' && $invoiceOutstanding)
                                                <tr>
                                                    <td>Outstanding Payment</td>
                                                    <td><span class="badge badge-primary">Payment</span></td>
                                                    <td>{{ $invoiceOutstanding->currency }} {{ number_format($invoiceOutstanding->sub_amount, 2) }}</td>
                                                    <td>{{ $invoiceOutstanding->currency }} {{ number_format($invoiceOutstanding->amount - $invoiceOutstanding->sub_amount, 2) }}</td>
                                                    @if($contract->job && $contract->job->is_emergency_hire)
                                                        <td>{{ $invoiceOutstanding->currency }} 0.00</td>
                                                    @endif
                                                    <td>{{ $invoiceOutstanding->currency }} {{ number_format($invoiceOutstanding->amount, 2) }}</td>
                                                </tr>
                                                @elseif($invoice->type == 'payment' && $invoiceEscrow)
                                                <tr>
                                                    <td>Escrow Payment</td>
                                                    <td><span class="badge badge-info">Escrow</span></td>
                                                    <td>{{ $invoiceEscrow->currency }} {{ number_format($invoiceEscrow->sub_amount, 2) }}</td>
                                                    <td>{{ $invoiceEscrow->currency }} {{ number_format($invoiceEscrow->amount - $invoiceEscrow->sub_amount, 2) }}</td>
                                                    @if($contract->job && $contract->job->is_emergency_hire)
                                                        <td>{{ $invoiceEscrow->currency }} 0.00</td>
                                                    @endif
                                                    <td>{{ $invoiceEscrow->currency }} {{ number_format($invoiceEscrow->amount, 2) }}</td>
                                                </tr>
                                                @endif
                                                <!-- Total Row -->
                                                @php
                                                    // Calculate total contract value from both invoices
                                                    $escrowTotal = $invoice->type === 'escrow' ? $invoice->amount : ($invoiceEscrow ? $invoiceEscrow->amount : 0);
                                                    $outstandingTotal = $invoice->type === 'payment' ? $invoice->amount : ($invoiceOutstanding ? $invoiceOutstanding->amount : 0);
                                                    $totalContractValue = $escrowTotal + $outstandingTotal;
                                                @endphp
                                                <tr class="font-weight-bold">
                                                    <td colspan="{{ ($contract->job && $contract->job->is_emergency_hire) ? '5' : '4' }}" class="text-right">Total Contract Value</td>
                                                    <td>{{ $invoice->currency }} {{ number_format($totalContractValue, 2) }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="invoice-detail-total mt-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card border mb-3">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Contract Details</h5>
                                                </div>
                                                <div class="card-body pt-2">
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Contract ID</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1">{{ $contract->id }}</p>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Contract Status</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1">
                                                                @php
                                                                // Map contract status to display text and badge class
                                                                $statusMap = [
                                                                    \App\Models\Contract::invited => ['class' => 'badge-info', 'text' => 'Invited'],
                                                                    \App\Models\Contract::pending => ['class' => 'badge-warning', 'text' => 'Pending'],
                                                                    \App\Models\Contract::in_progress => ['class' => 'badge-primary', 'text' => 'In Progress'],
                                                                    \App\Models\Contract::complete => ['class' => 'badge-success', 'text' => 'Complete'],
                                                                    \App\Models\Contract::canceled => ['class' => 'badge-danger', 'text' => 'Canceled'],
                                                                    \App\Models\Contract::rejected => ['class' => 'badge-secondary', 'text' => 'Rejected']
                                                                ];

                                                                $status = $contract->status ?? 0;
                                                                $statusInfo = $statusMap[$status] ?? ['class' => 'badge-secondary', 'text' => 'Unknown'];
                                                                @endphp
                                                                <span class="badge {{ $statusInfo['class'] }}">{{ $statusInfo['text'] }}</span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="card border mb-3">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Operative Details</h5>
                                                </div>
                                                <div class="card-body pt-2">
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Name</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1">{{ $contract->operative->name }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Email</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1">{{ $contract->operative->email }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Phone</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1">{{ $contract->operative->phone ?? 'N/A' }}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="card border h-100">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Payment Summary</h5>
                                                </div>
                                                <div class="card-body">
                                                    @php
                                                    // Get escrow payment details
                                                    $escrowAmount = $invoice->type == 'escrow' ? $invoice->amount : ($invoiceEscrow ? $invoiceEscrow->amount : 0);
                                                    $escrowPaid = $invoice->type == 'escrow' ? ($invoice->payment_status == 'paid') : ($invoiceEscrow && $invoiceEscrow->payment_status == 'paid');
                                                    $escrowInvoiceId = $invoice->type == 'escrow' ? $invoice->id : ($invoiceEscrow ? $invoiceEscrow->id : null);
                                                    $escrowDate = $invoice->type == 'escrow' ? $invoice->created_at : ($invoiceEscrow ? $invoiceEscrow->created_at : null);
                                                    
                                                    // Get regular payment details
                                                    $regularAmount = $invoice->type == 'payment' ? $invoice->amount : ($invoiceOutstanding ? $invoiceOutstanding->amount : 0);
                                                    $regularPaid = $invoice->type == 'payment' ? ($invoice->payment_status == 'paid') : ($invoiceOutstanding && $invoiceOutstanding->payment_status == 'paid');
                                                    $regularInvoiceId = $invoice->type == 'payment' ? $invoice->id : ($invoiceOutstanding ? $invoiceOutstanding->id : null);
                                                    $regularDate = $invoice->type == 'payment' ? $invoice->created_at : ($invoiceOutstanding ? $invoiceOutstanding->created_at : null);
                                                    
                                                    // Calculate total paid amount
                                                    $totalPaidAmount = 0;
                                                    if ($escrowPaid) $totalPaidAmount += $escrowAmount;
                                                    if ($regularPaid) $totalPaidAmount += $regularAmount;
                                                    
                                                    // Get contract total amount and calculate remaining amount
                                                    $totalContractAmount = $contract->total_amount;
                                                    $remainingAmount = $contract->total_amount - $totalPaidAmount;
                                                    $percentPaid = $contract->total_amount > 0 ? round(($totalPaidAmount / $contract->total_amount) * 100) : 0;
                                                    @endphp
                                                    
                                                    <div class="table-responsive">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>Payment Type</th>
                                                                    <th class="text-right">Amount</th>
                                                                    <th class="text-center">Status</th>
                                                                    <th class="text-center">Date</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($invoice->type == 'escrow' || $invoiceEscrow)
                                                                <tr>
                                                                    <td>
                                                                        <span class="badge badge-info">Escrow</span>
                                                                        <small class="d-block">
                                                                            Invoice #{{ $escrowInvoiceId }}
                                                                        </small>
                                                                        @if($escrowAmount > 0 && $contract->total_amount > 0)
                                                                        <small class="d-block text-muted">(@php echo round(($escrowAmount / $contract->total_amount) * 100); @endphp% of total)</small>
                                                                        @endif
                                                                    </td>
                                                                    <td class="text-right">
                                                                        {{ $invoice->currency }} {{ number_format($escrowAmount, 2) }}
                                                                    </td>
                                                                    <td class="text-center">
                                                                        <span class="badge {{ $escrowPaid ? 'badge-success' : 'badge-warning' }}">
                                                                            {{ $escrowPaid ? 'Paid' : 'Pending' }}
                                                                        </span>
                                                                    </td>
                                                                    <td class="text-center">
                                                                        {{ $escrowDate ? $escrowDate->format('d-m-Y') : 'N/A' }}
                                                                    </td>
                                                                </tr>
                                                                @endif
                                                                @if($invoice->type == 'payment' || $invoiceOutstanding)
                                                                <tr>
                                                                    <td>
                                                                        <span class="badge badge-primary">Regular Payment</span>
                                                                        <small class="d-block">
                                                                            Invoice #{{ $regularInvoiceId }}
                                                                        </small>
                                                                        @if($regularAmount > 0 && $contract->total_amount > 0)
                                                                        <small class="d-block text-muted">(@php echo round(($regularAmount / $contract->total_amount) * 100); @endphp% of total)</small>
                                                                        @endif
                                                                    </td>
                                                                    <td class="text-right">
                                                                        {{ $invoice->currency }} {{ number_format($regularAmount, 2) }}
                                                                    </td>
                                                                    <td class="text-center">
                                                                        <span class="badge {{ $regularPaid ? 'badge-success' : 'badge-warning' }}">
                                                                            {{ $regularPaid ? 'Paid' : 'Pending' }}
                                                                        </span>
                                                                    </td>
                                                                    <td class="text-center">
                                                                        {{ $regularDate ? $regularDate->format('d-m-Y') : 'N/A' }}
                                                                    </td>
                                                                </tr>
                                                                @endif
                                                                
                                                                <!-- Summary rows -->
                                                                <tr>
                                                                    <td colspan="4" class="pt-3 border-top"></td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>Total Paid</strong></td>
                                                                    <td class="text-right">{{ $invoice->currency }} {{ number_format($totalPaidAmount, 2) }}</td>
                                                                    <td colspan="2" class="text-center">
                                                                        @if($contract->total_amount > 0)
                                                                        <div class="progress" style="height: 20px;">
                                                                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ $percentPaid }}%;" aria-valuenow="{{ $percentPaid }}" aria-valuemin="0" aria-valuemax="100">{{ $percentPaid }}%</div>
                                                                        </div>
                                                                        @endif
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>Remaining Balance</strong></td>
                                                                    <td class="text-right">{{ $invoice->currency }} {{ number_format($remainingAmount, 2) }}</td>
                                                                    <td colspan="2"></td>
                                                                </tr>
                                                                <tr class="font-weight-bold" style="border-top: 2px solid #dee2e6">
                                                                    <td colspan="{{ ($contract->job && $contract->job->is_emergency_hire) ? '4' : '3' }}">Total Contract Value</td>
                                                                    <td>{{ $invoice->currency }} {{ number_format($contract->total_amount, 2) }}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="invoice-detail-note">
                                    <div class="row">
                                        <!-- Job Description Section -->
                                        <div class="col-md-6">
                                            <div class="card border mb-3">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Job Description</h5>
                                                </div>
                                                <div class="card-body">
                                                    <p class="mb-0">{{ $contract->job->description ?? 'No job description available.' }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Editable Notes Section -->
                                        <div class="col-md-6">
                                            <div class="card border">
                                                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">Notes</h5>
                                                    <button type="button" id="edit-notes-btn" class="btn btn-sm btn-outline-primary no-print">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </button>
                                                </div>
                                                <div class="card-body">
                                                    <!-- Display Mode -->
                                                    <div id="notes-display">
                                                        <p class="mb-0">{{ $contract->reason ?? 'No additional notes.' }}</p>
                                                    </div>

                                                    <!-- Edit Mode (hidden by default) -->
                                                    <div id="notes-edit" style="display: none;">
                                                        <form id="notes-form">
                                                            @csrf
                                                            <div class="form-group mb-3">
                                                                <textarea
                                                                    id="notes-textarea"
                                                                    name="notes"
                                                                    class="form-control"
                                                                    rows="4"
                                                                    placeholder="Add notes here (e.g., due date moved by 2 days)..."
                                                                >{{ $contract->reason }}</textarea>
                                                            </div>
                                                            <div class="d-flex gap-2">
                                                                <button type="submit" class="btn btn-sm btn-success">
                                                                    <i class="fas fa-save"></i> Save
                                                                </button>
                                                                <button type="button" id="cancel-edit-btn" class="btn btn-sm btn-secondary">
                                                                    <i class="fas fa-times"></i> Cancel
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4 no-print-buttons">
                        <div class="col-md-12">
                            <div class="invoice-actions-btn">
                                <div class="invoice-action-btn">
                                    <button onclick="window.print();" class="btn btn-secondary print-button">Print Invoice</button>
                                    <a href="{{ route('invoices.index') }}" class="btn btn-primary back-button">Back to Invoices</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@endsection

@section('styles')
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .invoice-container, .invoice-container * {
            visibility: visible;
        }
        .invoice-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            margin: 0;
            padding: 15px; /* Adjust as needed for A4 */
            font-size: 12pt; /* Adjust for print readability */
            border: none;
        }
        .layout-px-spacing {
            padding: 0 !important;
        }
        .widget-content {
            box-shadow: none;
            border: none;
        }
        .invoice-actions-btn,
        .back-button,
        .print-button,
        #edit-notes-btn,
        #notes-edit,
        .header-container, /* Assuming header is in layouts.app */
        .sidebar-wrapper, /* Assuming sidebar is in layouts.app */
        .footer-wrapper /* Assuming footer is in layouts.app */
        {
            display: none !important;
        }
        h4 {
            font-size: 16pt;
        }
        .table {
            font-size: 11pt;
        }
        .badge {
            border: 1px solid #ccc; /* Make badges more print-friendly */
            padding: .2em .4em;
        }
        /* Ensure no page breaks within important sections if possible */
        .invoice-detail-header, .invoice-detail-items, .invoice-detail-total, .invoice-detail-notes {
            page-break-inside: avoid;
        }
    }

    .invoice-detail-total .totals-row {
        border-top: 1px solid #e0e6ed;
        margin-top: 1rem;
        padding-top: 1rem;
    }
    .invoice-detail-total .totals-row {
        border-top: 1px solid #e0e6ed;
        margin-top: 1rem;
        padding-top: 1rem;
    }

    .invoice-detail-total .totals-row:last-child {
        border-top: 2px solid #e0e6ed;
        font-weight: bold;
        font-size: 1.125rem;
    }

    .invoice-totals-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }

    .invoice-summary-total {
        font-weight: bold;
        font-size: 1.125rem;
    }
</style>
@endsection

@section('scripts')
<script>
// Notes editing functionality
document.addEventListener('DOMContentLoaded', function() {
    const editBtn = document.getElementById('edit-notes-btn');
    const cancelBtn = document.getElementById('cancel-edit-btn');
    const notesDisplay = document.getElementById('notes-display');
    const notesEdit = document.getElementById('notes-edit');
    const notesForm = document.getElementById('notes-form');
    const notesTextarea = document.getElementById('notes-textarea');

    // Edit button click
    editBtn.addEventListener('click', function() {
        notesDisplay.style.display = 'none';
        notesEdit.style.display = 'block';
        editBtn.style.display = 'none';
        notesTextarea.focus();
    });

    // Cancel button click
    cancelBtn.addEventListener('click', function() {
        notesDisplay.style.display = 'block';
        notesEdit.style.display = 'none';
        editBtn.style.display = 'inline-block';
        // Reset textarea to original value
        notesTextarea.value = {!! json_encode($contract->reason ?? '') !!};
    });

    // Form submission
    notesForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData();
        formData.append('notes', notesTextarea.value);
        formData.append('_token', document.querySelector('input[name="_token"]').value);
        formData.append('_method', 'PUT');

        // Show loading state
        const saveBtn = notesForm.querySelector('button[type="submit"]');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        saveBtn.disabled = true;

        fetch(`/invoices/{{ $invoice->id }}/notes`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update display text
                const displayText = notesTextarea.value || 'No additional notes.';
                notesDisplay.querySelector('p').textContent = displayText;

                // Switch back to display mode
                notesDisplay.style.display = 'block';
                notesEdit.style.display = 'none';
                editBtn.style.display = 'inline-block';

                // Show success message (optional)
                // You can add a toast notification here if needed
            } else {
                alert('Error saving notes: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving notes. Please try again.');
        })
        .finally(() => {
            // Reset button state
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        });
    });
});
</script>
@endsection
