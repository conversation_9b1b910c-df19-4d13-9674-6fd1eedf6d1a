<?php
/**
 * GetWhatsappCampaignsCampaignsTest
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the model.
 */

namespace Brevo\Client;

/**
 * GetWhatsappCampaignsCampaignsTest Class Doc Comment
 *
 * @category    Class
 * @description GetWhatsappCampaignsCampaigns
 * @package     Brevo\Client
 * <AUTHOR> Codegen team
 * @link        https://github.com/swagger-api/swagger-codegen
 */
class GetWhatsappCampaignsCampaignsTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass()
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp()
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown()
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass()
    {
    }

    /**
     * Test "GetWhatsappCampaignsCampaigns"
     */
    public function testGetWhatsappCampaignsCampaigns()
    {
    }

    /**
     * Test attribute "id"
     */
    public function testPropertyId()
    {
    }

    /**
     * Test attribute "campaignName"
     */
    public function testPropertyCampaignName()
    {
    }

    /**
     * Test attribute "templateId"
     */
    public function testPropertyTemplateId()
    {
    }

    /**
     * Test attribute "campaignStatus"
     */
    public function testPropertyCampaignStatus()
    {
    }

    /**
     * Test attribute "scheduledAt"
     */
    public function testPropertyScheduledAt()
    {
    }

    /**
     * Test attribute "errorReason"
     */
    public function testPropertyErrorReason()
    {
    }

    /**
     * Test attribute "invalidatedContacts"
     */
    public function testPropertyInvalidatedContacts()
    {
    }

    /**
     * Test attribute "readPercentage"
     */
    public function testPropertyReadPercentage()
    {
    }

    /**
     * Test attribute "stats"
     */
    public function testPropertyStats()
    {
    }

    /**
     * Test attribute "createdAt"
     */
    public function testPropertyCreatedAt()
    {
    }

    /**
     * Test attribute "modifiedAt"
     */
    public function testPropertyModifiedAt()
    {
    }
}
