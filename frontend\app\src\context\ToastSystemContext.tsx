// @ts-nocheck
import React, { createContext, useContext, ReactNode } from 'react';
import useSurelyToast from '../hooks/useSurelyToast';

interface ToastSystemContextType {
  show: ReturnType<typeof useSurelyToast>['show'];
  hide: ReturnType<typeof useSurelyToast>['hide'];
  hideAll: ReturnType<typeof useSurelyToast>['hideAll'];
  showSuccess: ReturnType<typeof useSurelyToast>['showSuccess'];
  showError: ReturnType<typeof useSurelyToast>['showError'];
  showInfo: ReturnType<typeof useSurelyToast>['showInfo'];
  showWelcome: ReturnType<typeof useSurelyToast>['showWelcome'];
}

const ToastSystemContext = createContext<ToastSystemContextType | null>(null);

export const ToastProvider = ({ children }: { children: ReactNode }) => {
  const toast = useSurelyToast();

  return (
    <ToastSystemContext.Provider
      value={{
        show: toast.show,
        hide: toast.hide,
        hideAll: toast.hideAll,
        showSuccess: toast.showSuccess,
        showError: toast.showError,
        showInfo: toast.showInfo,
        showWelcome: toast.showWelcome,
      }}
    >
      {children}
      {toast.renderToasts()}
    </ToastSystemContext.Provider>
  );
};

export const useToastSystem = () => {
  const ctx = useContext(ToastSystemContext);
  if (!ctx) {
    throw new Error('useToastSystem must be used within a ToastProvider');
  }
  return ctx;
};

