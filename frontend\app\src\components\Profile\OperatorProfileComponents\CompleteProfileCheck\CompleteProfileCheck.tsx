// @ts-nocheck
import React, { useState, useContext } from 'react';
import { View, Text, Progress, useToggle } from 'reshaped';
import InfoTooltip from 'src/components/common/InfoTooltip';
import { useNavigate } from 'react-router-dom';
import CompleteProfileModal from '../CompleteProfileModal/CompleteProfileModal';

const CompleteProfileCheck: React.FC = () => {
  const navigate = useNavigate();
  const { active, activate, deactivate } = useToggle(false);
  const [completenessValue, setCompletenessValue] = useState<number>(20);

  const handleCompletenessChange = (value: number) => {
    setCompletenessValue(value);
  };

  return (
    <View className='flex flex-col mt-10 sm:mt-0'>
      <View className='flex flex-row gap-2'>
        <Text className='font-medium rubik text-[#lalala] mt-[0px] mr-[0px]'>
          Profile completeness: {completenessValue}%
        </Text>
        <InfoTooltip text='Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile.' />
      </View>
      <Progress value={completenessValue} color='positive' className='mt-2' />
      <button
        className='btn-no-hover text-[#078549] rubik text-[14px] font-medium leading-[20px] underline mt-[16px]'
        style={{ textAlign: 'left', display: 'block', width: 'fit-content' }}
        onClick={activate}
      >
        Complete your profile
      </button>

      <CompleteProfileModal
        active={active}
        deactivate={deactivate}
        onCompletenessChange={handleCompletenessChange}
      />
    </View>
  );
};

export default CompleteProfileCheck;
