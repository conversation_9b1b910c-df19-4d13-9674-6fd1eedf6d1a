<?php

namespace App\Traits;
use Illuminate\Support\Facades\Storage;

trait Helpers
{
    public function base64Upload($pathName, $file): ?string
    {
        if (str_contains($file, ',')) {
            // Extract the base64 data from the given string
            [$encoding, $imageData] = explode(',', $file, 2);

            $mimeInfo = explode(';', $encoding);
            $extension = explode('/', $mimeInfo[0])[1];

            $imageName = $pathName . '_' . now().'-'.uniqid('', true) . '.' . $extension;
            $path = $pathName . '/' . $imageName;

            Storage::disk('public')->put($path, base64_decode($imageData));

            return $path;
        } else {
            // Handle the case when $file is not in the expected format or we can handle this differently, depending on your requirements
            return null;
        }
    }

    public static function userLastActivityStatus($timestamp): ?string
    {
        $lastSeenFormat = $timestamp?->isToday() ? "Last seen today at {$timestamp?->format('H:i')}" : ($timestamp?->isYesterday()
            ? "Last seen yesterday at {$timestamp?->format('H:i')}"
            : "Last seen at {$timestamp?->format('d/m/Y H:i')}"
        );

        return $timestamp?->gt(now()->subSeconds(5)) ? 'Online' : $lastSeenFormat;
    }

    public function printQueryWithValuesBindings($query): string
    {
        //Note: Replace the query with your query variable without using get()
        $sql = $query->toSql();
        // Get the bindings
        $bindings = $query->getBindings();
        // Combine the query and bindings into a complete, executable query
        return vsprintf(str_replace('?', '%s', $sql), $bindings);
    }
}
