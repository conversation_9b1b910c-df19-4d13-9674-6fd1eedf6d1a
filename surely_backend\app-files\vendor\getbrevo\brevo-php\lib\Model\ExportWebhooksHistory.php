<?php
/**
 * ExportWebhooksHistory
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * ExportWebhooksHistory Class Doc Comment
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class ExportWebhooksHistory implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'exportWebhooksHistory';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'days' => 'int',
        'startDate' => 'string',
        'endDate' => 'string',
        'sort' => 'string',
        'type' => 'string',
        'event' => 'string',
        'notifyURL' => 'string',
        'webhookId' => 'int',
        'email' => 'string',
        'messageId' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'days' => null,
        'startDate' => null,
        'endDate' => null,
        'sort' => null,
        'type' => null,
        'event' => null,
        'notifyURL' => null,
        'webhookId' => null,
        'email' => null,
        'messageId' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'days' => 'days',
        'startDate' => 'startDate',
        'endDate' => 'endDate',
        'sort' => 'sort',
        'type' => 'type',
        'event' => 'event',
        'notifyURL' => 'notifyURL',
        'webhookId' => 'webhookId',
        'email' => 'email',
        'messageId' => 'messageId'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'days' => 'setDays',
        'startDate' => 'setStartDate',
        'endDate' => 'setEndDate',
        'sort' => 'setSort',
        'type' => 'setType',
        'event' => 'setEvent',
        'notifyURL' => 'setNotifyURL',
        'webhookId' => 'setWebhookId',
        'email' => 'setEmail',
        'messageId' => 'setMessageId'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'days' => 'getDays',
        'startDate' => 'getStartDate',
        'endDate' => 'getEndDate',
        'sort' => 'getSort',
        'type' => 'getType',
        'event' => 'getEvent',
        'notifyURL' => 'getNotifyURL',
        'webhookId' => 'getWebhookId',
        'email' => 'getEmail',
        'messageId' => 'getMessageId'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    const TYPE_TRANSACTIONAL = 'transactional';
    const TYPE_MARKETING = 'marketing';
    const EVENT_INVALID_PARAMETER = 'invalid_parameter';
    const EVENT_MISSING_PARAMETER = 'missing_parameter';
    const EVENT_HARD_BOUNCE = 'hardBounce';
    const EVENT_SOFT_BOUNCE = 'softBounce';
    const EVENT_DELIVERED = 'delivered\"';
    const EVENT_SPAM = 'spam';
    const EVENT_REQUEST = 'request';
    const EVENT_OPENED = 'opened';
    const EVENT_CLICK = 'click';
    const EVENT_INVALID = 'invalid';
    const EVENT_DEFERRED = 'deferred';
    const EVENT_BLOCKED = 'blocked';
    const EVENT_UNSUBSCRIBED = 'unsubscribed';
    const EVENT_ERROR = 'error';
    const EVENT_UNIQUE_OPENED = 'uniqueOpened';
    const EVENT_LOADED_BY_PROXY = 'loadedByProxy';
    const EVENT_ALL_EVENTS = 'allEvents';
    

    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getTypeAllowableValues()
    {
        return [
            self::TYPE_TRANSACTIONAL,
            self::TYPE_MARKETING,
        ];
    }
    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getEventAllowableValues()
    {
        return [
            self::EVENT_INVALID_PARAMETER,
            self::EVENT_MISSING_PARAMETER,
            self::EVENT_HARD_BOUNCE,
            self::EVENT_SOFT_BOUNCE,
            self::EVENT_DELIVERED,
            self::EVENT_SPAM,
            self::EVENT_REQUEST,
            self::EVENT_OPENED,
            self::EVENT_CLICK,
            self::EVENT_INVALID,
            self::EVENT_DEFERRED,
            self::EVENT_BLOCKED,
            self::EVENT_UNSUBSCRIBED,
            self::EVENT_ERROR,
            self::EVENT_UNIQUE_OPENED,
            self::EVENT_LOADED_BY_PROXY,
            self::EVENT_ALL_EVENTS,
        ];
    }
    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['days'] = isset($data['days']) ? $data['days'] : null;
        $this->container['startDate'] = isset($data['startDate']) ? $data['startDate'] : null;
        $this->container['endDate'] = isset($data['endDate']) ? $data['endDate'] : null;
        $this->container['sort'] = isset($data['sort']) ? $data['sort'] : null;
        $this->container['type'] = isset($data['type']) ? $data['type'] : null;
        $this->container['event'] = isset($data['event']) ? $data['event'] : null;
        $this->container['notifyURL'] = isset($data['notifyURL']) ? $data['notifyURL'] : null;
        $this->container['webhookId'] = isset($data['webhookId']) ? $data['webhookId'] : null;
        $this->container['email'] = isset($data['email']) ? $data['email'] : null;
        $this->container['messageId'] = isset($data['messageId']) ? $data['messageId'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['type'] === null) {
            $invalidProperties[] = "'type' can't be null";
        }
        $allowedValues = $this->getTypeAllowableValues();
        if (!is_null($this->container['type']) && !in_array($this->container['type'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'type', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        if ($this->container['event'] === null) {
            $invalidProperties[] = "'event' can't be null";
        }
        $allowedValues = $this->getEventAllowableValues();
        if (!is_null($this->container['event']) && !in_array($this->container['event'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'event', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        if ($this->container['notifyURL'] === null) {
            $invalidProperties[] = "'notifyURL' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets days
     *
     * @return int
     */
    public function getDays()
    {
        return $this->container['days'];
    }

    /**
     * Sets days
     *
     * @param int $days Number of days in the past including today (positive integer). _Not compatible with 'startDate' and 'endDate'_
     *
     * @return $this
     */
    public function setDays($days)
    {
        $this->container['days'] = $days;

        return $this;
    }

    /**
     * Gets startDate
     *
     * @return string
     */
    public function getStartDate()
    {
        return $this->container['startDate'];
    }

    /**
     * Sets startDate
     *
     * @param string $startDate Mandatory if endDate is used. Starting date of the history (YYYY-MM-DD). Must be lower than equal to endDate
     *
     * @return $this
     */
    public function setStartDate($startDate)
    {
        $this->container['startDate'] = $startDate;

        return $this;
    }

    /**
     * Gets endDate
     *
     * @return string
     */
    public function getEndDate()
    {
        return $this->container['endDate'];
    }

    /**
     * Sets endDate
     *
     * @param string $endDate Mandatory if startDate is used. Ending date of the report (YYYY-MM-DD). Must be greater than equal to startDate
     *
     * @return $this
     */
    public function setEndDate($endDate)
    {
        $this->container['endDate'] = $endDate;

        return $this;
    }

    /**
     * Gets sort
     *
     * @return string
     */
    public function getSort()
    {
        return $this->container['sort'];
    }

    /**
     * Sets sort
     *
     * @param string $sort Sorting order of records (asc or desc)
     *
     * @return $this
     */
    public function setSort($sort)
    {
        $this->container['sort'] = $sort;

        return $this;
    }

    /**
     * Gets type
     *
     * @return string
     */
    public function getType()
    {
        return $this->container['type'];
    }

    /**
     * Sets type
     *
     * @param string $type Filter the history based on webhook type
     *
     * @return $this
     */
    public function setType($type)
    {
        $allowedValues = $this->getTypeAllowableValues();
        if (!in_array($type, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'type', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['type'] = $type;

        return $this;
    }

    /**
     * Gets event
     *
     * @return string
     */
    public function getEvent()
    {
        return $this->container['event'];
    }

    /**
     * Sets event
     *
     * @param string $event Filter the history for a specific event type
     *
     * @return $this
     */
    public function setEvent($event)
    {
        $allowedValues = $this->getEventAllowableValues();
        if (!in_array($event, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'event', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['event'] = $event;

        return $this;
    }

    /**
     * Gets notifyURL
     *
     * @return string
     */
    public function getNotifyURL()
    {
        return $this->container['notifyURL'];
    }

    /**
     * Sets notifyURL
     *
     * @param string $notifyURL Webhook URL to receive CSV file link
     *
     * @return $this
     */
    public function setNotifyURL($notifyURL)
    {
        $this->container['notifyURL'] = $notifyURL;

        return $this;
    }

    /**
     * Gets webhookId
     *
     * @return int
     */
    public function getWebhookId()
    {
        return $this->container['webhookId'];
    }

    /**
     * Sets webhookId
     *
     * @param int $webhookId Filter the history for a specific webhook id
     *
     * @return $this
     */
    public function setWebhookId($webhookId)
    {
        $this->container['webhookId'] = $webhookId;

        return $this;
    }

    /**
     * Gets email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->container['email'];
    }

    /**
     * Sets email
     *
     * @param string $email Filter the history for a specific email
     *
     * @return $this
     */
    public function setEmail($email)
    {
        $this->container['email'] = $email;

        return $this;
    }

    /**
     * Gets messageId
     *
     * @return int
     */
    public function getMessageId()
    {
        return $this->container['messageId'];
    }

    /**
     * Sets messageId
     *
     * @param int $messageId Filter the history for a specific message id. Applicable only for transactional webhooks.
     *
     * @return $this
     */
    public function setMessageId($messageId)
    {
        $this->container['messageId'] = $messageId;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


