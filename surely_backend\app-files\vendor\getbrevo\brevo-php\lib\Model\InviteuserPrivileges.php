<?php
/**
 * InviteuserPrivileges
 *
 * PHP version 5
 *
 * @category Class
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * Brevo API
 *
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |
 *
 * OpenAPI spec version: 3.0.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.29
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Brevo\Client\Model;

use \ArrayAccess;
use \Brevo\Client\ObjectSerializer;

/**
 * InviteuserPrivileges Class Doc Comment
 *
 * @category Class
 * @description Privileges given to the user
 * @package  Brevo\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class InviteuserPrivileges implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'inviteuser_privileges';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'feature' => 'string',
        'permissions' => 'string[]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'feature' => null,
        'permissions' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'feature' => 'feature',
        'permissions' => 'permissions'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'feature' => 'setFeature',
        'permissions' => 'setPermissions'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'feature' => 'getFeature',
        'permissions' => 'getPermissions'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    const FEATURE_EMAIL_CAMPAIGNS = 'email_campaigns';
    const FEATURE_SMS_CAMPAIGNS = 'sms_campaigns';
    const FEATURE_CONTACTS = 'contacts';
    const FEATURE_TEMPLATES = 'templates';
    const FEATURE_WORKFLOWS = 'workflows';
    const FEATURE_FACEBOOK_ADS = 'facebook_ads';
    const FEATURE_LANDING_PAGES = 'landing_pages';
    const FEATURE_TRANSACTIONAL_EMAILS = 'transactional_emails';
    const FEATURE_SMTP_API = 'smtp_api';
    const FEATURE_USER_MANAGEMENT = 'user_management';
    const FEATURE_SALES_PLATFORM = 'sales_platform';
    const FEATURE_PHONE = 'phone';
    const FEATURE_CONVERSATIONS = 'conversations';
    const FEATURE_SENDERS_DOMAINS_DEDICATED_IPS = 'senders_domains_dedicated_ips';
    const FEATURE_PUSH_NOTIFICATIONS = 'push_notifications';
    const PERMISSIONS_CREATE_EDIT_DELETE = 'create_edit_delete';
    const PERMISSIONS_SEND_SCHEDULE_SUSPEND = 'send_schedule_suspend';
    const PERMISSIONS_VIEW = 'view';
    const PERMISSIONS_IMPORT = 'import';
    const PERMISSIONS_EXPORT = 'export';
    const PERMISSIONS_LIST_AND_ATTRIBUTES = 'list_and_attributes';
    const PERMISSIONS_FORMS = 'forms';
    const PERMISSIONS_ACTIVATE_DEACTIVATE = 'activate_deactivate';
    const PERMISSIONS_ACTIVATE_DEACTIVATE_PAUSE = 'activate_deactivate_pause';
    const PERMISSIONS_SETTINGS = 'settings';
    const PERMISSIONS_SCHEDULE_PAUSE = 'schedule_pause';
    const PERMISSIONS_ALL = 'all';
    const PERMISSIONS_LOGS = 'logs';
    const PERMISSIONS_ACCESS = 'access';
    const PERMISSIONS_ASSIGN = 'assign';
    const PERMISSIONS_CONFIGURE = 'configure';
    const PERMISSIONS_MANAGE_OWNED_DEALS_TASKS_COMPANIES = 'manage_owned_deals_tasks_companies';
    const PERMISSIONS_MANAGE_OTHERS_DEALS_TASKS_COMPANIES = 'manage_others_deals_tasks_companies';
    const PERMISSIONS_REPORTS = 'reports';
    const PERMISSIONS_SENDERS_MANAGEMENT = 'senders_management';
    const PERMISSIONS_DOMAINS_MANAGEMENT = 'domains_management';
    const PERMISSIONS_DEDICATED_IPS_MANAGEMENT = 'dedicated_ips_management';
    const PERMISSIONS_SEND = 'send';
    const PERMISSIONS_SMTP = 'smtp';
    const PERMISSIONS_API_KEYS = 'api_keys';
    const PERMISSIONS_AUTHORIZED_IPS = 'authorized_ips';
    const PERMISSIONS_NONE = 'none';
    

    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getFeatureAllowableValues()
    {
        return [
            self::FEATURE_EMAIL_CAMPAIGNS,
            self::FEATURE_SMS_CAMPAIGNS,
            self::FEATURE_CONTACTS,
            self::FEATURE_TEMPLATES,
            self::FEATURE_WORKFLOWS,
            self::FEATURE_FACEBOOK_ADS,
            self::FEATURE_LANDING_PAGES,
            self::FEATURE_TRANSACTIONAL_EMAILS,
            self::FEATURE_SMTP_API,
            self::FEATURE_USER_MANAGEMENT,
            self::FEATURE_SALES_PLATFORM,
            self::FEATURE_PHONE,
            self::FEATURE_CONVERSATIONS,
            self::FEATURE_SENDERS_DOMAINS_DEDICATED_IPS,
            self::FEATURE_PUSH_NOTIFICATIONS,
        ];
    }
    
    /**
     * Gets allowable values of the enum
     *
     * @return string[]
     */
    public function getPermissionsAllowableValues()
    {
        return [
            self::PERMISSIONS_CREATE_EDIT_DELETE,
            self::PERMISSIONS_SEND_SCHEDULE_SUSPEND,
            self::PERMISSIONS_VIEW,
            self::PERMISSIONS_IMPORT,
            self::PERMISSIONS_EXPORT,
            self::PERMISSIONS_LIST_AND_ATTRIBUTES,
            self::PERMISSIONS_FORMS,
            self::PERMISSIONS_ACTIVATE_DEACTIVATE,
            self::PERMISSIONS_ACTIVATE_DEACTIVATE_PAUSE,
            self::PERMISSIONS_SETTINGS,
            self::PERMISSIONS_SCHEDULE_PAUSE,
            self::PERMISSIONS_ALL,
            self::PERMISSIONS_LOGS,
            self::PERMISSIONS_ACCESS,
            self::PERMISSIONS_ASSIGN,
            self::PERMISSIONS_CONFIGURE,
            self::PERMISSIONS_MANAGE_OWNED_DEALS_TASKS_COMPANIES,
            self::PERMISSIONS_MANAGE_OTHERS_DEALS_TASKS_COMPANIES,
            self::PERMISSIONS_REPORTS,
            self::PERMISSIONS_SENDERS_MANAGEMENT,
            self::PERMISSIONS_DOMAINS_MANAGEMENT,
            self::PERMISSIONS_DEDICATED_IPS_MANAGEMENT,
            self::PERMISSIONS_SEND,
            self::PERMISSIONS_SMTP,
            self::PERMISSIONS_API_KEYS,
            self::PERMISSIONS_AUTHORIZED_IPS,
            self::PERMISSIONS_NONE,
        ];
    }
    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['feature'] = isset($data['feature']) ? $data['feature'] : null;
        $this->container['permissions'] = isset($data['permissions']) ? $data['permissions'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        $allowedValues = $this->getFeatureAllowableValues();
        if (!is_null($this->container['feature']) && !in_array($this->container['feature'], $allowedValues, true)) {
            $invalidProperties[] = sprintf(
                "invalid value for 'feature', must be one of '%s'",
                implode("', '", $allowedValues)
            );
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets feature
     *
     * @return string
     */
    public function getFeature()
    {
        return $this->container['feature'];
    }

    /**
     * Sets feature
     *
     * @param string $feature Feature name
     *
     * @return $this
     */
    public function setFeature($feature)
    {
        $allowedValues = $this->getFeatureAllowableValues();
        if (!is_null($feature) && !in_array($feature, $allowedValues, true)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'feature', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['feature'] = $feature;

        return $this;
    }

    /**
     * Gets permissions
     *
     * @return string[]
     */
    public function getPermissions()
    {
        return $this->container['permissions'];
    }

    /**
     * Sets permissions
     *
     * @param string[] $permissions Permissions for a given feature
     *
     * @return $this
     */
    public function setPermissions($permissions)
    {
        $allowedValues = $this->getPermissionsAllowableValues();
        if (!is_null($permissions) && array_diff($permissions, $allowedValues)) {
            throw new \InvalidArgumentException(
                sprintf(
                    "Invalid value for 'permissions', must be one of '%s'",
                    implode("', '", $allowedValues)
                )
            );
        }
        $this->container['permissions'] = $permissions;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    #[\ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    #[\ReturnTypeWillChange]
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


