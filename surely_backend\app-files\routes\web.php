<?php

use App\Http\Controllers\Api\Freelancer\JobController;
use App\Http\Controllers\Api\NewsLetterController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\Datatables\MobileUserDatatable;
use App\Http\Controllers\Datatables\SettingsDatatable;
use App\Http\Controllers\Datatables\UserDatatable;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\ExpertiseFieldController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\MobileUserController;
use App\Http\Controllers\Api\SupportController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\UserController;
use App\Http\Livewire\EmailRequest;
use App\Models\NewsLetter;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes(['register' => false]);


Route::middleware('auth')->group(function () {

    Route::get('home', [HomeController::class, 'index'])->name('home');
    Route::get('/', [HomeController::class, 'index']);

    Route::middleware('admin')->group(function () {
        Route::prefix('users')->group(function () {
            Route::get('users', [UserController::class, 'index'])->name('users');
            Route::post('new_user', [UserController::class, 'store'])->name('new_user');
            Route::post('edit_user/{user}', [UserController::class, 'update'])->name('edit_user');
            Route::get('profile', [UserController::class, 'profile'])->name('profile');
            Route::post('change_password', [UserController::class, 'change_password'])->name('change_password');
        });

        Route::prefix('mobile_users')->group(function () {
            Route::get('mobile_users', [MobileUserController::class, 'index'])->name('mobile_users');
            Route::post('change_status_user/{mobileUser}', [MobileUserController::class, 'change_status'])->name('change_status_user');
            Route::get('{id}/documents', [DocumentController::class, 'index']);
            Route::post('{id}/documents/verification', [DocumentController::class, 'documentsApproval']);
            Route::get('status/{id}', [MobileUserController::class, 'change_status']);
        });

        Route::namespace('Datatables')->group(function () {

            /* users */
            Route::get('usersData', [UserDatatable::class, 'usersData'])->name('usersData');
            Route::get('usersModal/{id}/{action}', [UserDatatable::class, 'loadModal'])->name('usersModal');

            /* mobile users */
            Route::get('mobileUsersData', [MobileUserDatatable::class, 'mobileUsersData'])->name('mobileUsersData');
            Route::get('mobileUsersModal/{id}/{action}', [MobileUserDatatable::class, 'loadModal'])->name('mobileUsersModal');
            Route::get('settingsModal/{id}/{action}', [SettingsDatatable::class, 'loadModal'])->name('settingsModal');

        });

        Route::get('supports', [SupportController::class, 'table'])->name('supports_table');
        Route::get('reviews', [ReviewController::class, 'table'])->name('reviews_table');
        Route::get('reports', [ReportController::class, 'table'])->name('reports_table');
        Route::get('newsletter', [NewsLetterController::class, 'table'])->name('newsletter_table');
        Route::get('email-requests', EmailRequest::class)->name('email_requests_table');
        
        // Invoices routes
        Route::get('invoices', [InvoiceController::class, 'index'])->name('invoices.index');
        Route::get('invoices/data', [InvoiceController::class, 'getInvoicesData'])->name('invoices.data');
        Route::get('invoices/debug', [InvoiceController::class, 'debug'])->name('invoices.debug');
        Route::get('invoices/{id}', [InvoiceController::class, 'show'])->name('invoices.show');
        Route::put('invoices/{id}/notes', [InvoiceController::class, 'updateNotes'])->name('invoices.updateNotes');
    });

    Route::resource('blogs', BlogController::class, ['names' => 'blogs']);
    Route::get('jobs', [JobController::class, 'table'])->name('jobs_table');

    Route::get('payment', [PaymentController::class, 'payment'])->name('payment');
    Route::post('/create-payment-intent', [PaymentController::class, 'createPaymentIntent'])->name('create.payment.intent');
    Route::post('/handle-payment', [PaymentController::class, 'handlePayment'])->name('handle.payment');
    Route::get('/payment-success', [PaymentController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/payment-error', [PaymentController::class, 'paymentError'])->name('payment.error');
});