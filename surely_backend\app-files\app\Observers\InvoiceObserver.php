<?php

namespace App\Observers;

use App\Models\Invoice;
use Illuminate\Support\Facades\Log;

class InvoiceObserver
{
    /**
     * Handle the Invoice "updated" event.
     * When an invoice status changes to 'paid', recalculate the contract's payment status
     */
    public function updated(Invoice $invoice): void
    {
        // Check if status was changed and is now 'paid'
        if ($invoice->isDirty('status') && $invoice->status === 'paid') {
            $contract = $invoice->contract;
            
            if ($contract) {
                try {
                    $contract->recalculatePaymentStatus();
                    Log::info('Contract payment status recalculated after invoice paid', [
                        'contract_id' => $contract->id,
                        'invoice_id' => $invoice->id,
                        'new_payment_status' => $contract->payment_status
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to recalculate contract payment status', [
                        'contract_id' => $contract->id,
                        'invoice_id' => $invoice->id,
                        'error' => $e->getMessage()
                    ]);
                }
            } else {
                Log::warning('Paid invoice has no associated contract', [
                    'invoice_id' => $invoice->id
                ]);
            }
        }
    }
}
